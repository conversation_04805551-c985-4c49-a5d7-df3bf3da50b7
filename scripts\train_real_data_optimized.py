#!/usr/bin/env python3
"""
Optimized training script for real human behavior data.
Configured specifically for the actual data dimensions and optimal performance.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import torch

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from human_behavior_ai.human_behavior_ai.models.main import create_model
from human_behavior_ai.human_behavior_ai.models.base import ModelConfig
from human_behavior_ai.human_behavior_ai.training.hybrid_trainer import Hybrid<PERSON>ehavior<PERSON>rainer, HybridTrainingConfig
from human_behavior_ai.human_behavior_ai.data.loader import create_data_loaders
from human_behavior_ai.human_behavior_ai.utils.logging import setup_logging
from human_behavior_ai.human_behavior_ai.utils.random import set_seed
from human_behavior_ai.human_behavior_ai.utils.device import get_memory_usage


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Optimized training for real human behavior data')
    
    # Data configuration
    parser.add_argument('--data-path', type=Path, required=True,
                       help='Path to training data directory')
    parser.add_argument('--epochs', type=int, default=200,
                       help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=4,
                       help='Batch size (optimized for 4GB GPU)')
    parser.add_argument('--learning-rate', type=float, default=2e-4,
                       help='Learning rate')
    
    # Memory optimization
    parser.add_argument('--gradient-accumulation', type=int, default=32,
                       help='Gradient accumulation steps')
    parser.add_argument('--sequence-length', type=int, default=1024,
                       help='Maximum sequence length')
    
    # Output
    parser.add_argument('--output-dir', type=Path, default='experiments/real_data_optimized',
                       help='Output directory')
    parser.add_argument('--no-wandb', action='store_true',
                       help='Disable Weights & Biases logging')
    
    # Debugging
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    return parser.parse_args()


def setup_memory_optimizations():
    """Setup memory optimizations for CUDA."""
    if torch.cuda.is_available():
        # Set memory fraction
        torch.cuda.set_per_process_memory_fraction(0.85)
        
        # Enable memory efficient attention
        torch.backends.cuda.enable_flash_sdp(True)
        torch.backends.cuda.enable_mem_efficient_sdp(True)
        torch.backends.cuda.enable_math_sdp(False)
        
        # Set CUDA allocator settings
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
        
        # Clear cache
        torch.cuda.empty_cache()
        
        print(f"CUDA optimizations enabled for {torch.cuda.get_device_name()}")
        print(f"Available CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")


def create_optimized_model_config():
    """Create model configuration optimized for real data dimensions."""
    
    # Real data dimensions (from analysis):
    # mouse_data: 6 dimensions (x, y, vx, vy, vmag, dt)
    # keyboard_data: 1 dimension (zeros - no keyboard data)
    # scroll_data: 4 dimensions (dx, dy, dz, mode)
    # context_data: 4 dimensions (remaining features)
    # Total: 15 dimensions
    
    config = ModelConfig(
        # Model architecture (optimized for 4GB GPU)
        hidden_dim=256,
        num_layers=8,
        num_heads=8,
        sequence_length=1024,  # 4x to handle 4 modalities * 256 sequence length
        
        # Real data dimensions
        mouse_dim=6,      # x, y, vx, vy, vmag, dt
        keyboard_dim=1,   # No keyboard data in current format
        scroll_dim=4,     # dx, dy, dz, mode
        context_dim=4,    # Remaining context features
        
        # Input/Output dimensions
        input_dim=15,     # Total: 6+1+4+4 = 15
        output_dim=15,    # Same as input for autoregressive modeling
        
        # Training settings
        learning_rate=2e-4,
        dropout=0.1,
        
        # Optimizations
        use_positional_encoding=True,
        use_layer_norm=True,
        use_residual_connections=True,
        use_attention_dropout=True,
        
        # Model-specific optimizations for real data
        model_specific={
            # Transformer optimizations
            'use_flash_attention': True,
            'use_rotary_embeddings': False,  # Disable to save memory
            'gradient_checkpointing': True,
            
            # VAE optimizations
            'latent_dim': 128,
            'beta': 0.1,
            'use_hierarchical_prior': False,
            
            # GAN optimizations
            'use_spectral_norm': False,
            'gradient_penalty_weight': 5.0,
            
            # Hybrid optimizations
            'transformer_weight': 0.5,
            'vae_weight': 0.25,
            'gan_weight': 0.25,
            'fusion_strategy': 'mlp',
        }
    )
    
    return config


def main():
    """Main training function."""
    args = parse_args()
    
    # Setup logging
    setup_logging(level=logging.DEBUG if args.debug else logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Set random seed
    set_seed(args.seed)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Setup memory optimizations
    setup_memory_optimizations()
    
    # Log initial memory usage
    memory_info = get_memory_usage()
    logger.info(f"Initial memory usage: {memory_info}")
    
    # Create optimized model
    logger.info("Creating optimized model for real data...")
    config = create_optimized_model_config()
    model = create_model(model_type='hybrid', **config.to_dict())
    logger.info(f"Model created with {model.model.get_num_parameters():,} parameters")
    
    # Log memory after model creation
    memory_info = get_memory_usage()
    logger.info(f"Memory after model creation: {memory_info}")
    
    # Create data loaders
    logger.info("Creating data loaders for real data...")
    train_loader, val_loader, _ = create_data_loaders(
        data_dir=str(args.data_path),
        batch_size=args.batch_size,
        num_workers=0,  # Avoid pickling issues
        seed=args.seed
    )
    logger.info(f"Data loaders created - Train: {len(train_loader)} batches, Val: {len(val_loader)} batches")
    
    # Create training configuration
    training_config = HybridTrainingConfig(
        num_epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        weight_decay=1e-5,
        mixed_precision=True,
        gradient_accumulation_steps=args.gradient_accumulation,
        checkpoint_frequency=2000,
        validation_frequency=1000,
        log_frequency=50,
        output_dir=str(output_dir),
        wandb_project=None if args.no_wandb else "human-behavior-ai-real-data",
        num_workers=0,
        pin_memory=False,
    )
    
    # Create trainer
    logger.info("Creating trainer...")
    trainer = HybridBehaviorTrainer(model, training_config, train_loader, val_loader)
    
    # Log memory before training
    memory_info = get_memory_usage()
    logger.info(f"Memory before training: {memory_info}")
    
    # Start training
    logger.info("Starting optimized training with real data...")
    try:
        history = trainer.train()
        logger.info("Training completed successfully!")
        
        # Save final model
        final_model_path = output_dir / 'final_model'
        model.save_pretrained(final_model_path)
        logger.info(f"Final model saved to {final_model_path}")
        
        # Log final memory usage
        memory_info = get_memory_usage()
        logger.info(f"Final memory usage: {memory_info}")
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error(f"CUDA out of memory: {e}")
        logger.error("Try reducing batch size or sequence length further")
        return 1
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
