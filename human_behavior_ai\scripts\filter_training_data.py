#!/usr/bin/env python3
"""
Training Data Filter
Removes stationary periods and improves training data quality.
"""

import json
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple
import argparse
import shutil
from datetime import datetime


class TrainingDataFilter:
    """Filters and cleans training data to remove problematic patterns."""
    
    def __init__(self, input_dir: str, output_dir: str):
        """Initialize the filter."""
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Filtering parameters
        self.min_movement_threshold = 5  # Minimum pixels to consider movement
        self.max_stationary_period = 10  # Max consecutive stationary events
        self.min_velocity_threshold = 0.1  # Minimum velocity to keep event
    
    def filter_session_data(self, events: List[Dict]) -> List[Dict]:
        """Filter events to remove excessive stationary periods."""
        if not events:
            return events
        
        filtered_events = []
        stationary_count = 0
        last_position = None
        
        for i, event in enumerate(events):
            if event.get('type') != 'mouse_position':
                # Keep non-mouse events as-is
                filtered_events.append(event)
                continue
            
            data = event.get('data', {})
            position = data.get('position', {})
            velocity = data.get('velocity', {})
            
            current_pos = (position.get('x', 0), position.get('y', 0))
            velocity_mag = velocity.get('magnitude', 0)
            
            # Check if mouse is stationary
            is_stationary = velocity_mag < self.min_velocity_threshold
            
            if last_position is not None:
                # Calculate actual movement distance
                distance = np.sqrt((current_pos[0] - last_position[0])**2 + 
                                 (current_pos[1] - last_position[1])**2)
                is_stationary = is_stationary or distance < self.min_movement_threshold
            
            if is_stationary:
                stationary_count += 1
                
                # Only keep a few stationary events to maintain natural pauses
                if stationary_count <= self.max_stationary_period:
                    # Modify the event to add small movement to avoid perfect stationarity
                    modified_event = self._add_micro_movement(event, i)
                    filtered_events.append(modified_event)
                # Skip excessive stationary events
            else:
                # Reset stationary count and keep the event
                stationary_count = 0
                filtered_events.append(event)
            
            last_position = current_pos
        
        return filtered_events
    
    def _add_micro_movement(self, event: Dict, event_index: int) -> Dict:
        """Add small micro-movements to stationary events to make them more natural."""
        modified_event = event.copy()
        data = modified_event.get('data', {}).copy()
        position = data.get('position', {}).copy()
        velocity = data.get('velocity', {}).copy()
        
        # Add small random micro-movement (1-2 pixels)
        import random
        micro_x = random.uniform(-1.5, 1.5)
        micro_y = random.uniform(-1.5, 1.5)
        
        position['x'] = int(position.get('x', 0) + micro_x)
        position['y'] = int(position.get('y', 0) + micro_y)
        
        # Add small velocity to indicate micro-movement
        velocity['x'] = micro_x * 10  # Scale up for velocity
        velocity['y'] = micro_y * 10
        velocity['magnitude'] = np.sqrt(velocity['x']**2 + velocity['y']**2)
        
        data['position'] = position
        data['velocity'] = velocity
        modified_event['data'] = data
        
        return modified_event
    
    def enhance_movement_data(self, events: List[Dict]) -> List[Dict]:
        """Enhance existing movement data with more natural patterns."""
        enhanced_events = []
        
        for i, event in enumerate(events):
            if event.get('type') != 'mouse_position':
                enhanced_events.append(event)
                continue
            
            data = event.get('data', {})
            velocity = data.get('velocity', {})
            velocity_mag = velocity.get('magnitude', 0)
            
            # If this is a movement event, potentially add intermediate points
            if velocity_mag > 5 and i < len(events) - 1:
                next_event = events[i + 1]
                if next_event.get('type') == 'mouse_position':
                    # Add intermediate points for smoother movement
                    intermediate_events = self._generate_intermediate_points(event, next_event)
                    enhanced_events.extend(intermediate_events)
                else:
                    enhanced_events.append(event)
            else:
                enhanced_events.append(event)
        
        return enhanced_events
    
    def _generate_intermediate_points(self, start_event: Dict, end_event: Dict) -> List[Dict]:
        """Generate intermediate points between two movement events."""
        start_data = start_event.get('data', {})
        end_data = end_event.get('data', {})
        
        start_pos = start_data.get('position', {})
        end_pos = end_data.get('position', {})
        
        start_x, start_y = start_pos.get('x', 0), start_pos.get('y', 0)
        end_x, end_y = end_pos.get('x', 0), end_pos.get('y', 0)
        
        # Calculate distance
        distance = np.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
        
        # Only add intermediate points for longer movements
        if distance < 20:
            return [start_event]
        
        # Calculate number of intermediate points
        num_points = min(3, int(distance / 15))
        
        events = [start_event]
        
        start_time = start_event.get('timestamp', 0)
        end_time = end_event.get('timestamp', 0)
        time_diff = end_time - start_time
        
        for i in range(1, num_points + 1):
            t = i / (num_points + 1)
            
            # Linear interpolation for position
            interp_x = int(start_x + (end_x - start_x) * t)
            interp_y = int(start_y + (end_y - start_y) * t)
            
            # Calculate velocity
            dt = time_diff / (num_points + 1)
            if dt > 0:
                vx = (end_x - start_x) / (time_diff / 1000)  # pixels per second
                vy = (end_y - start_y) / (time_diff / 1000)
                vmag = np.sqrt(vx**2 + vy**2)
            else:
                vx = vy = vmag = 0
            
            # Create intermediate event
            intermediate_event = {
                'type': 'mouse_position',
                'timestamp': start_time + int(i * dt),
                'data': {
                    'position': {'x': interp_x, 'y': interp_y},
                    'velocity': {'x': vx, 'y': vy, 'magnitude': vmag},
                    'deltaTime': dt / 1000,
                    'taskId': start_data.get('taskId', 1)
                }
            }
            events.append(intermediate_event)
        
        return events
    
    def process_session_file(self, input_file: Path) -> bool:
        """Process a single session file."""
        try:
            print(f"📝 Processing: {input_file.name}")
            
            # Load events data
            with open(input_file, 'r') as f:
                events = json.load(f)
            
            original_count = len(events)
            print(f"   📊 Original events: {original_count:,}")
            
            # Apply filters
            filtered_events = self.filter_session_data(events)
            filtered_count = len(filtered_events)
            print(f"   🔍 After filtering: {filtered_count:,} ({filtered_count/original_count*100:.1f}%)")
            
            # Enhance movement data
            enhanced_events = self.enhance_movement_data(filtered_events)
            final_count = len(enhanced_events)
            print(f"   ✨ After enhancement: {final_count:,}")
            
            # Save filtered data
            output_file = self.output_dir / input_file.name
            with open(output_file, 'w') as f:
                json.dump(enhanced_events, f, indent=2)
            
            # Copy metadata file if it exists
            metadata_file = input_file.with_name(input_file.stem.replace('_data', '') + '.json')
            if metadata_file.exists():
                output_metadata = self.output_dir / metadata_file.name
                
                # Update metadata
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                metadata['dataPoints'] = final_count
                metadata['filtered'] = True
                metadata['filterDate'] = datetime.now().isoformat()
                metadata['originalDataPoints'] = original_count
                
                with open(output_metadata, 'w') as f:
                    json.dump(metadata, f, indent=2)
            
            print(f"   ✅ Saved to: {output_file.name}")
            return True
            
        except Exception as e:
            print(f"   ❌ Error processing {input_file.name}: {e}")
            return False
    
    def process_all_sessions(self):
        """Process all session files in the input directory."""
        print("🔧 Training Data Filter")
        print("=" * 50)
        
        # Find all data files
        data_files = list(self.input_dir.glob("*_data.json"))
        
        if not data_files:
            print("❌ No session data files found!")
            return
        
        print(f"📁 Found {len(data_files)} session data files")
        print(f"📤 Input directory: {self.input_dir}")
        print(f"📥 Output directory: {self.output_dir}")
        
        successful = 0
        failed = 0
        
        for data_file in data_files:
            if self.process_session_file(data_file):
                successful += 1
            else:
                failed += 1
        
        print(f"\n📊 Processing Summary:")
        print(f"   ✅ Successful: {successful}")
        print(f"   ❌ Failed: {failed}")
        print(f"   📈 Success rate: {successful/(successful+failed)*100:.1f}%")
        
        if successful > 0:
            print(f"\n🎉 Filtered data saved to: {self.output_dir}")
            print("💡 Use this filtered data for retraining to fix the stationary behavior!")


def main():
    """Main filtering function."""
    parser = argparse.ArgumentParser(description='Filter training data to remove stationary periods')
    parser.add_argument('--input-dir', default='data/sessions', help='Input directory with session data')
    parser.add_argument('--output-dir', default='data/sessions_filtered', help='Output directory for filtered data')
    parser.add_argument('--min-movement', type=float, default=5, help='Minimum movement threshold (pixels)')
    parser.add_argument('--max-stationary', type=int, default=10, help='Maximum consecutive stationary events')
    
    args = parser.parse_args()
    
    # Initialize filter
    filter_tool = TrainingDataFilter(args.input_dir, args.output_dir)
    filter_tool.min_movement_threshold = args.min_movement
    filter_tool.max_stationary_period = args.max_stationary
    
    # Process all sessions
    filter_tool.process_all_sessions()


if __name__ == "__main__":
    main()
