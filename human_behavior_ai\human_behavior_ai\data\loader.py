"""
Data loading utilities for behavioral datasets.

This module provides data loaders and utilities for efficiently
loading and batching behavioral sequence data.
"""

import torch
from torch.utils.data import DataLoader as TorchDataLoader, Dataset
from typing import Dict, List, Optional, Tuple, Any, Union
import numpy as np
from pathlib import Path

from .dataset import InteractionDataset, BehaviorSequence
from .processor import DataProcessor, ProcessingConfig


class SequenceDataset(Dataset):
    """Dataset for pre-processed behavior sequences."""

    def __init__(self, sequences: List[BehaviorSequence]):
        self.sequences = sequences

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return self.sequences[idx]


class DataLoader:
    """
    Custom data loader for behavioral interaction data.
    
    Provides functionality for loading, processing, and batching
    behavioral sequences with various configurations.
    """
    
    def __init__(
        self,
        data_dir: Union[str, Path],
        processing_config: Optional[ProcessingConfig] = None,
        batch_size: int = 32,
        shuffle: bool = True,
        num_workers: int = 0,
        pin_memory: bool = True
    ):
        self.data_dir = Path(data_dir)
        self.processing_config = processing_config or ProcessingConfig()
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.num_workers = num_workers
        self.pin_memory = pin_memory
        
        # Initialize processor
        self.processor = DataProcessor(self.processing_config)
        
        # Load and process data
        self.dataset = self._load_dataset()
        
        # Create PyTorch DataLoader
        self.dataloader = TorchDataLoader(
            self.dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=pin_memory,
            collate_fn=self._collate_fn
        )
    
    def _load_dataset(self) -> InteractionDataset:
        """Load and process all session files in the data directory."""
        session_files = list(self.data_dir.glob("*.json"))
        
        if not session_files:
            raise ValueError(f"No JSON files found in {self.data_dir}")
        
        all_sequences = []
        
        # Process each session file
        for session_file in session_files:
            try:
                sequences = self.processor.process_session_file(session_file)
                all_sequences.extend(sequences)
            except Exception as e:
                print(f"Warning: Failed to process {session_file}: {e}")
                continue
        
        if not all_sequences:
            raise ValueError("No valid sequences found in the data directory")
        
        # Normalize sequences
        all_sequences = self.processor.normalize_sequences(all_sequences)
        
        return InteractionDataset(all_sequences)
    
    def _collate_fn(self, batch: List[BehaviorSequence]) -> Dict[str, torch.Tensor]:
        """
        Custom collate function for batching behavior sequences.
        
        Args:
            batch: List of BehaviorSequence objects
            
        Returns:
            Dictionary containing batched tensors
        """
        # Extract features and pad sequences to same length
        features = [seq.features for seq in batch]
        task_types = [seq.task_type for seq in batch]
        
        # Pad sequences to maximum length in batch
        max_length = max(seq.size(0) for seq in features)
        padded_features = []
        attention_masks = []
        
        for seq in features:
            seq_len = seq.size(0)
            feature_dim = seq.size(1)
            
            # Pad sequence
            if seq_len < max_length:
                padding = torch.zeros(max_length - seq_len, feature_dim)
                padded_seq = torch.cat([seq, padding], dim=0)
                mask = torch.cat([torch.ones(seq_len), torch.zeros(max_length - seq_len)])
            else:
                padded_seq = seq
                mask = torch.ones(seq_len)
            
            padded_features.append(padded_seq)
            attention_masks.append(mask)
        
        # Stack into batch tensors
        batch_features = torch.stack(padded_features)
        batch_masks = torch.stack(attention_masks)
        
        # Encode task types
        task_type_to_id = {
            'mouse_tracking': 0,
            'click_sequence': 1,
            'drag_drop': 2,
            'scroll_test': 3,
            'typing_test': 4,
            'unknown': 5
        }
        
        task_ids = torch.tensor([task_type_to_id.get(task_type, 5) for task_type in task_types])
        
        return {
            'sequences': batch_features,
            'attention_mask': batch_masks,
            'task_types': task_ids,
            'sequence_lengths': torch.tensor([seq.size(0) for seq in features])
        }
    
    def __iter__(self):
        """Make the DataLoader iterable."""
        return iter(self.dataloader)
    
    def __len__(self):
        """Return the number of batches."""
        return len(self.dataloader)


def create_data_loaders(
    data_dir: Union[str, Path, List[str]],
    processing_config: Optional[ProcessingConfig] = None,
    train_split: float = 0.8,
    val_split: float = 0.1,
    test_split: float = 0.1,
    batch_size: int = 32,
    num_workers: int = 0,
    seed: int = 42
) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create train, validation, and test data loaders.
    
    Args:
        data_dir: Directory containing session JSON files
        processing_config: Configuration for data processing
        train_split: Fraction of data for training
        val_split: Fraction of data for validation
        test_split: Fraction of data for testing
        batch_size: Batch size for data loaders
        num_workers: Number of worker processes
        seed: Random seed for reproducible splits
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    # Validate splits
    if abs(train_split + val_split + test_split - 1.0) > 1e-6:
        raise ValueError("Train, validation, and test splits must sum to 1.0")
    
    # Handle different input types
    if isinstance(data_dir, list):
        # If data_dir is a list, treat it as a list of file paths
        session_files = [Path(f) for f in data_dir]
    else:
        try:
            # If data_dir is a string or Path, treat it as a directory
            data_dir = Path(data_dir)
            session_files = list(data_dir.glob("*.json"))
        except TypeError as e:
            # If conversion to Path fails, raise a more helpful error
            raise TypeError(f"data_dir must be a string, Path, or list of file paths, got {type(data_dir)}")
    
    processing_config = processing_config or ProcessingConfig()
    
    if not session_files:
        raise ValueError(f"No JSON files found in {data_dir if not isinstance(data_dir, list) else 'provided file list'}")
    
    # Shuffle files for random split
    np.random.seed(seed)
    np.random.shuffle(session_files)
    
    # Calculate split indices
    n_files = len(session_files)
    train_end = int(n_files * train_split)
    val_end = train_end + int(n_files * val_split)
    
    # Split files
    train_files = session_files[:train_end]
    val_files = session_files[train_end:val_end]
    test_files = session_files[val_end:]
    
    # Create temporary directories for splits (or process files directly)
    processor = DataProcessor(processing_config)
    
    # Process files for each split
    def process_files(files: List[Path]) -> List[BehaviorSequence]:
        all_sequences = []
        for file_path in files:
            try:
                sequences = processor.process_session_file(file_path)
                all_sequences.extend(sequences)
            except Exception as e:
                print(f"Warning: Failed to process {file_path}: {e}")
                continue
        return all_sequences
    
    # Process each split
    train_sequences = process_files(train_files)
    val_sequences = process_files(val_files)
    test_sequences = process_files(test_files)
    
    # Compute normalization stats on training data if sequences exist
    if train_sequences:
        processor.compute_normalization_stats(train_sequences)
        
        # Normalize all splits using training stats
        train_sequences = processor.normalize_sequences(train_sequences)
        val_sequences = processor.normalize_sequences(val_sequences)
        test_sequences = processor.normalize_sequences(test_sequences)
    else:
        raise ValueError("No training sequences found after processing")
    
    # Create datasets
    train_dataset = SequenceDataset(train_sequences)
    val_dataset = SequenceDataset(val_sequences)
    test_dataset = SequenceDataset(test_sequences)

    # Create data loaders
    train_loader = TorchDataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        collate_fn=_collate_fn
    )
    
    val_loader = TorchDataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        collate_fn=_collate_fn
    )
    
    test_loader = TorchDataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        collate_fn=_collate_fn
    )
    
    # Wrap in custom DataLoader objects for compatibility
    train_data_loader = DataLoader.__new__(DataLoader)
    train_data_loader.dataloader = train_loader
    train_data_loader.dataset = train_dataset
    
    val_data_loader = DataLoader.__new__(DataLoader)
    val_data_loader.dataloader = val_loader
    val_data_loader.dataset = val_dataset
    
    test_data_loader = DataLoader.__new__(DataLoader)
    test_data_loader.dataloader = test_loader
    test_data_loader.dataset = test_dataset
    
    return train_data_loader, val_data_loader, test_data_loader


def _collate_fn(batch: List[BehaviorSequence]) -> Dict[str, torch.Tensor]:
    """
    Standalone collate function for use with PyTorch DataLoader.

    Args:
        batch: List of BehaviorSequence objects

    Returns:
        Dictionary containing batched tensors
    """
    # Extract data from sequences
    mouse_data = [torch.FloatTensor(seq.mouse_data) for seq in batch]
    keyboard_data = [torch.FloatTensor(seq.keyboard_data) for seq in batch]
    scroll_data = [torch.FloatTensor(seq.scroll_data) for seq in batch]
    context_data = [torch.FloatTensor(seq.context_data) for seq in batch]

    # Find maximum length in batch, but limit to reasonable size
    max_length = min(max(len(seq) for seq in batch), 256)  # Limit to 256 for memory efficiency

    # Pad sequences to maximum length
    def pad_sequence(sequences, max_len):
        padded = []
        masks = []
        for seq in sequences:
            seq_len = seq.size(0)
            if seq_len < max_len:
                # Pad with zeros
                padding = torch.zeros(max_len - seq_len, seq.size(1))
                padded_seq = torch.cat([seq, padding], dim=0)
                mask = torch.cat([torch.ones(seq_len), torch.zeros(max_len - seq_len)])
            else:
                padded_seq = seq[:max_len]  # Truncate if too long
                mask = torch.ones(max_len)
            padded.append(padded_seq)
            masks.append(mask)
        return torch.stack(padded), torch.stack(masks)

    # Pad all modalities
    mouse_padded, mouse_mask = pad_sequence(mouse_data, max_length)
    keyboard_padded, keyboard_mask = pad_sequence(keyboard_data, max_length)
    scroll_padded, scroll_mask = pad_sequence(scroll_data, max_length)
    context_padded, context_mask = pad_sequence(context_data, max_length)

    # Combine all modalities into sequences
    sequences = torch.cat([mouse_padded, keyboard_padded, scroll_padded, context_padded], dim=-1)

    # Create targets (next step prediction)
    targets = torch.roll(sequences, -1, dims=1)
    targets[:, -1] = sequences[:, -1]  # Last target is same as last input

    # Create rewards (dummy for now)
    rewards = torch.ones(sequences.size(0), sequences.size(1))

    return {
        'sequences': sequences,
        'targets': targets,
        'rewards': rewards,
        'mouse': mouse_padded,
        'keyboard': keyboard_padded,
        'scroll': scroll_padded,
        'context': context_padded,
        'attention_mask': mouse_mask,  # Use mouse mask as general mask
    }


class SequenceDataset(Dataset):
    """
    PyTorch Dataset wrapper for behavior sequences.
    
    Provides a simple interface for accessing individual sequences
    in a format compatible with PyTorch DataLoader.
    """
    
    def __init__(self, sequences: List[BehaviorSequence]):
        self.sequences = sequences
    
    def __len__(self) -> int:
        return len(self.sequences)
    
    def __getitem__(self, idx: int) -> BehaviorSequence:
        return self.sequences[idx]
    
    def get_feature_dim(self) -> int:
        """Get the feature dimension of sequences."""
        if self.sequences:
            return self.sequences[0].features.size(1)
        return 0
    
    def get_task_types(self) -> List[str]:
        """Get unique task types in the dataset."""
        return list(set(seq.task_type for seq in self.sequences))
    
    def filter_by_task_type(self, task_type: str) -> 'SequenceDataset':
        """Create a new dataset filtered by task type."""
        filtered_sequences = [seq for seq in self.sequences if seq.task_type == task_type]
        return SequenceDataset(filtered_sequences)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get dataset statistics."""
        if not self.sequences:
            return {}
        
        sequence_lengths = [seq.features.size(0) for seq in self.sequences]
        task_types = [seq.task_type for seq in self.sequences]
        
        # Task type distribution
        task_type_counts = {}
        for task_type in task_types:
            task_type_counts[task_type] = task_type_counts.get(task_type, 0) + 1
        
        return {
            'num_sequences': len(self.sequences),
            'feature_dim': self.get_feature_dim(),
            'sequence_lengths': {
                'min': min(sequence_lengths),
                'max': max(sequence_lengths),
                'mean': np.mean(sequence_lengths),
                'std': np.std(sequence_lengths)
            },
            'task_type_distribution': task_type_counts
        }
