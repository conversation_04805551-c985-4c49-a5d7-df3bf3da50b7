"""
AI Behavior Generator for Human-like Interactions
Uses the trained hybrid model to generate realistic human behavior patterns.
"""

import sys
import os
import torch
import numpy as np
import asyncio
import random
import time
from pathlib import Path
from typing import List, Tuple, Dict, Any
import json
import math

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from human_behavior_ai.human_behavior_ai.models.main import create_model
    from human_behavior_ai.human_behavior_ai.models.base import ModelConfig
    MODEL_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import AI model: {e}")
    print("Falling back to mathematical simulation...")
    MODEL_AVAILABLE = False


class AIBehaviorGenerator:
    """Generates human-like behavior patterns using the trained AI model."""
    
    def __init__(self, model_path: str = None):
        """Initialize the AI behavior generator."""
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_loaded = False
        
        # Fallback parameters for mathematical simulation
        self.mouse_noise_scale = 2.0
        self.typing_base_delay = 0.1
        self.scroll_smoothness = 0.8
        
        if MODEL_AVAILABLE and model_path:
            self.load_model(model_path)
    
    def load_model(self, model_path: str):
        """Load the trained AI model."""
        try:
            if not Path(model_path).exists():
                print(f"Model path {model_path} not found, using mathematical simulation")
                return
            
            # Create model configuration matching the trained model
            config = ModelConfig(
                hidden_dim=256,
                num_layers=8,
                num_heads=8,
                sequence_length=1024,
                mouse_dim=6,
                keyboard_dim=1,
                scroll_dim=4,
                context_dim=4,
                input_dim=15,
                output_dim=15,
                learning_rate=2e-4,
                dropout=0.1,
                use_positional_encoding=True,
                use_layer_norm=True,
                use_residual_connections=True,
                use_attention_dropout=True,
                model_specific={
                    'use_flash_attention': True,
                    'use_rotary_embeddings': False,
                    'gradient_checkpointing': True,
                    'latent_dim': 128,
                    'beta': 0.1,
                    'use_hierarchical_prior': False,
                    'use_spectral_norm': False,
                    'gradient_penalty_weight': 5.0,
                    'transformer_weight': 0.5,
                    'vae_weight': 0.25,
                    'gan_weight': 0.25,
                    'fusion_strategy': 'mlp',
                }
            )
            
            # Create and load model
            self.model = create_model(model_type='hybrid', **config.to_dict())

            # Try to load the model, but handle compatibility issues gracefully
            try:
                self.model.load_pretrained(model_path)
                self.model.to(self.device)
                self.model.eval()
                self.model_loaded = True
            except Exception as load_error:
                print(f"⚠️ Model loading issue: {load_error}")
                print("🔄 Model created but not loaded - using mathematical simulation")
                self.model_loaded = False
            
            print(f"✅ AI model loaded successfully from {model_path}")
            print(f"🧠 Model parameters: {self.model.model.get_num_parameters():,}")
            print(f"🔧 Device: {self.device}")
            
        except Exception as e:
            print(f"❌ Failed to load AI model: {e}")
            print("🔄 Falling back to mathematical simulation")
            self.model_loaded = False
    
    def generate_mouse_trajectory(self, start_x: int, start_y: int, 
                                end_x: int, end_y: int, 
                                duration: float = 1.0) -> List[Tuple[int, int, float]]:
        """Generate human-like mouse trajectory between two points."""
        
        if self.model_loaded:
            return self._generate_ai_mouse_trajectory(start_x, start_y, end_x, end_y, duration)
        else:
            return self._generate_mathematical_mouse_trajectory(start_x, start_y, end_x, end_y, duration)
    
    def _generate_ai_mouse_trajectory(self, start_x: int, start_y: int, 
                                    end_x: int, end_y: int, 
                                    duration: float) -> List[Tuple[int, int, float]]:
        """Generate trajectory using the AI model."""
        try:
            # Create input sequence representing current mouse state
            sequence_length = min(64, int(duration * 60))  # 60 FPS simulation
            
            # Initialize sequence with current mouse position
            mouse_data = np.zeros((sequence_length, 6))  # x, y, vx, vy, vmag, dt
            mouse_data[0] = [start_x, start_y, 0, 0, 0, 1/60]
            
            # Create target trajectory as context
            for i in range(1, sequence_length):
                progress = i / (sequence_length - 1)
                target_x = start_x + (end_x - start_x) * progress
                target_y = start_y + (end_y - start_y) * progress
                mouse_data[i] = [target_x, target_y, 0, 0, 0, 1/60]
            
            # Prepare model input
            keyboard_data = np.zeros((sequence_length, 1))
            scroll_data = np.zeros((sequence_length, 4))
            context_data = np.zeros((sequence_length, 4))
            
            # Combine all modalities
            sequences = np.concatenate([mouse_data, keyboard_data, scroll_data, context_data], axis=1)
            
            # Convert to tensor and add batch dimension
            input_tensor = torch.FloatTensor(sequences).unsqueeze(0).to(self.device)
            
            # Generate with model
            with torch.no_grad():
                batch = {
                    'sequences': input_tensor,
                    'mouse': torch.FloatTensor(mouse_data).unsqueeze(0).to(self.device),
                    'keyboard': torch.FloatTensor(keyboard_data).unsqueeze(0).to(self.device),
                    'scroll': torch.FloatTensor(scroll_data).unsqueeze(0).to(self.device),
                    'context': torch.FloatTensor(context_data).unsqueeze(0).to(self.device),
                }
                
                output = self.model.forward(batch)
                
                # Extract mouse predictions
                if 'transformer_mouse_predictions' in output:
                    predictions = output['transformer_mouse_predictions'][0].cpu().numpy()
                elif 'vae_reconstruction' in output:
                    predictions = output['vae_reconstruction'][0, :, :6].cpu().numpy()
                else:
                    # Fallback to mathematical generation
                    return self._generate_mathematical_mouse_trajectory(start_x, start_y, end_x, end_y, duration)
            
            # Convert predictions to trajectory points
            trajectory = []
            time_step = duration / len(predictions)
            
            for i, pred in enumerate(predictions):
                x, y = int(pred[0]), int(pred[1])
                timestamp = i * time_step
                trajectory.append((x, y, timestamp))
            
            return trajectory
            
        except Exception as e:
            print(f"AI trajectory generation failed: {e}, using mathematical fallback")
            return self._generate_mathematical_mouse_trajectory(start_x, start_y, end_x, end_y, duration)
    
    def _generate_mathematical_mouse_trajectory(self, start_x: int, start_y: int,
                                              end_x: int, end_y: int,
                                              duration: float) -> List[Tuple[int, int, float]]:
        """Generate trajectory using mathematical simulation of human movement."""

        # Calculate distance and steps
        distance = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)

        # CRITICAL FIX: Ensure minimum movement even for short distances
        if distance < 5:
            # For very short distances, create a small natural movement
            steps = 8
        else:
            steps = max(15, int(distance / 3))  # More steps for smoother movement

        trajectory = []

        # Generate control points for Bezier curve (human-like curved movement)
        control_points = self._generate_control_points(start_x, start_y, end_x, end_y)

        for i in range(steps + 1):
            t = i / steps

            # Apply easing function for natural acceleration/deceleration
            eased_t = self._ease_in_out_cubic(t)

            # Calculate position using Bezier curve
            x, y = self._bezier_curve(control_points, eased_t)

            # CRITICAL FIX: Add progressive movement to avoid stationary periods
            # Ensure the mouse is always moving towards the target
            if i > 0 and i < steps:
                # Add small progressive movement even if Bezier curve is flat
                progress_x = start_x + (end_x - start_x) * eased_t
                progress_y = start_y + (end_y - start_y) * eased_t

                # Blend Bezier curve with direct movement to ensure progress
                blend_factor = 0.7  # 70% Bezier, 30% direct movement
                x = x * blend_factor + progress_x * (1 - blend_factor)
                y = y * blend_factor + progress_y * (1 - blend_factor)

            # Add human-like noise and micro-movements (reduced to avoid stationary periods)
            noise_x = random.gauss(0, self.mouse_noise_scale * 0.5)
            noise_y = random.gauss(0, self.mouse_noise_scale * 0.5)

            x += noise_x
            y += noise_y

            # Calculate timestamp with slight variations
            time_variation = random.gauss(0, 0.005)  # Reduced variation
            timestamp = (duration * t) + time_variation

            trajectory.append((int(x), int(y), max(0, timestamp)))

        # CRITICAL FIX: Ensure the trajectory actually reaches the target
        if len(trajectory) > 0:
            trajectory[-1] = (end_x, end_y, duration)

        return trajectory
    
    def _generate_control_points(self, start_x: int, start_y: int,
                               end_x: int, end_y: int) -> List[Tuple[float, float]]:
        """Generate control points for natural curved movement."""

        # Calculate distance and direction
        distance = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)

        # CRITICAL FIX: Handle very short distances to avoid stationary periods
        if distance < 5:
            # For very short distances, create a small natural arc
            mid_x = (start_x + end_x) / 2
            mid_y = (start_y + end_y) / 2

            # Add a small perpendicular offset to create movement
            offset = 8  # Small but noticeable movement
            angle = random.uniform(0, 2 * math.pi)
            offset_x = offset * math.cos(angle)
            offset_y = offset * math.sin(angle)

            control1_x = start_x + offset_x * 0.3
            control1_y = start_y + offset_y * 0.3
            control2_x = end_x - offset_x * 0.3
            control2_y = end_y - offset_y * 0.3
        else:
            # Normal distance - create natural curved movement
            mid_x = (start_x + end_x) / 2
            mid_y = (start_y + end_y) / 2

            # Add natural curve by offsetting control points
            curve_intensity = min(80, distance * 0.15)  # Increased curve for more natural movement

            # Perpendicular offset for natural curve
            dx = end_x - start_x
            dy = end_y - start_y

            perp_x = -dy / distance * curve_intensity * random.uniform(-1, 1)
            perp_y = dx / distance * curve_intensity * random.uniform(-1, 1)

            # Create control points that ensure smooth progression
            control1_x = start_x + (end_x - start_x) * 0.25 + perp_x * 0.6
            control1_y = start_y + (end_y - start_y) * 0.25 + perp_y * 0.6

            control2_x = start_x + (end_x - start_x) * 0.75 + perp_x * 0.4
            control2_y = start_y + (end_y - start_y) * 0.75 + perp_y * 0.4

        return [
            (start_x, start_y),
            (control1_x, control1_y),
            (control2_x, control2_y),
            (end_x, end_y)
        ]
    
    def _bezier_curve(self, points: List[Tuple[float, float]], t: float) -> Tuple[float, float]:
        """Calculate point on cubic Bezier curve."""
        p0, p1, p2, p3 = points
        
        x = (1-t)**3 * p0[0] + 3*(1-t)**2*t * p1[0] + 3*(1-t)*t**2 * p2[0] + t**3 * p3[0]
        y = (1-t)**3 * p0[1] + 3*(1-t)**2*t * p1[1] + 3*(1-t)*t**2 * p2[1] + t**3 * p3[1]
        
        return x, y
    
    def _ease_in_out_cubic(self, t: float) -> float:
        """Cubic easing function for natural acceleration/deceleration."""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2
    
    def generate_typing_pattern(self, text: str) -> List[Tuple[str, float]]:
        """Generate human-like typing pattern with realistic delays."""
        
        if self.model_loaded:
            return self._generate_ai_typing_pattern(text)
        else:
            return self._generate_mathematical_typing_pattern(text)
    
    def _generate_ai_typing_pattern(self, text: str) -> List[Tuple[str, float]]:
        """Generate typing pattern using AI model."""
        # For now, use mathematical simulation as AI typing requires more complex implementation
        return self._generate_mathematical_typing_pattern(text)
    
    def _generate_mathematical_typing_pattern(self, text: str) -> List[Tuple[str, float]]:
        """Generate typing pattern using mathematical simulation."""
        
        pattern = []
        cumulative_time = 0
        
        for i, char in enumerate(text):
            # Base delay with variations
            base_delay = self.typing_base_delay
            
            # Character-specific delays
            if char == ' ':
                delay = base_delay * random.uniform(1.5, 3.0)  # Longer pause for spaces
            elif char in '.,!?;:':
                delay = base_delay * random.uniform(1.2, 2.0)  # Pause for punctuation
            elif char.isupper():
                delay = base_delay * random.uniform(1.1, 1.5)  # Slight delay for capitals
            else:
                delay = base_delay * random.uniform(0.8, 1.2)  # Normal variation
            
            # Add typing rhythm variations
            if i > 0 and i % random.randint(5, 15) == 0:
                delay *= random.uniform(1.5, 2.5)  # Occasional longer pauses
            
            # Simulate occasional typos and corrections
            if random.random() < 0.02:  # 2% chance of typo
                # Add wrong character
                wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                pattern.append((wrong_char, cumulative_time))
                cumulative_time += delay * 0.5
                
                # Add backspace
                pattern.append(('\b', cumulative_time))
                cumulative_time += delay * 0.3
                
                # Add correct character
                pattern.append((char, cumulative_time))
                cumulative_time += delay * 0.7
            else:
                pattern.append((char, cumulative_time))
                cumulative_time += delay
        
        return pattern
    
    def generate_scroll_pattern(self, target_position: int, 
                              current_position: int = 0,
                              duration: float = 2.0) -> List[Tuple[int, float]]:
        """Generate human-like scrolling pattern."""
        
        if self.model_loaded:
            return self._generate_ai_scroll_pattern(target_position, current_position, duration)
        else:
            return self._generate_mathematical_scroll_pattern(target_position, current_position, duration)
    
    def _generate_ai_scroll_pattern(self, target_position: int, 
                                  current_position: int, 
                                  duration: float) -> List[Tuple[int, float]]:
        """Generate scroll pattern using AI model."""
        # For now, use mathematical simulation
        return self._generate_mathematical_scroll_pattern(target_position, current_position, duration)
    
    def _generate_mathematical_scroll_pattern(self, target_position: int, 
                                            current_position: int, 
                                            duration: float) -> List[Tuple[int, float]]:
        """Generate scroll pattern using mathematical simulation."""
        
        distance = target_position - current_position
        if abs(distance) < 10:
            return [(target_position, 0)]
        
        steps = max(10, int(abs(distance) / 20))
        pattern = []
        
        for i in range(steps + 1):
            t = i / steps
            
            # Apply easing for natural scrolling
            eased_t = self._ease_in_out_cubic(t)
            
            # Calculate position
            position = current_position + distance * eased_t
            
            # Add natural variations
            if i > 0 and i < steps:
                variation = random.gauss(0, abs(distance) * 0.01)
                position += variation
            
            # Calculate timestamp with variations
            time_variation = random.gauss(0, duration * 0.02)
            timestamp = (duration * t) + time_variation
            
            pattern.append((int(position), max(0, timestamp)))
        
        return pattern
