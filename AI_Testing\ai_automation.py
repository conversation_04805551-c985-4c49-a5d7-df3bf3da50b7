"""
AI-Powered Human Behavior Automation using NoDriver
Implements sophisticated stealth techniques and AI-generated human behavior.
"""

import asyncio
import random
import time
import os
from pathlib import Path
from typing import List, Tuple, Dict, Any
import json

try:
    import nodriver as uc
    from nodriver import cdp
    NODRIVER_AVAILABLE = True
except ImportError:
    print("❌ NoDriver not available. Install with: pip install nodriver")
    NODRIVER_AVAILABLE = False

from ai_behavior_generator import AIBehaviorGenerator


class AIAutomation:
    """Advanced AI-powered automation with human behavior simulation."""
    
    def __init__(self, model_path: str = None):
        """Initialize the AI automation system."""
        self.browser = None
        self.tab = None
        self.ai_generator = AIBehaviorGenerator(model_path)
        self.viewport_width = 1920
        self.viewport_height = 1080
        self.current_mouse_x = 0
        self.current_mouse_y = 0
        
    async def start_browser(self):
        """Start browser with advanced stealth configuration."""
        if not NODRIVER_AVAILABLE:
            raise RuntimeError("NoDriver is not available")
        
        print("🚀 Starting stealth browser...")
        
        # Advanced stealth configuration
        browser_args = [
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-dev-shm-usage',
            '--no-sandbox',
            '--disable-gpu',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-field-trial-config',
            '--disable-back-forward-cache',
            '--disable-ipc-flooding-protection',
            '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        # Start browser with stealth settings
        self.browser = await uc.start(
            browser_args=browser_args,
            headless=False,
            sandbox=True,
            user_data_dir=None  # Fresh profile each time
        )
        
        # Get main tab
        self.tab = self.browser.main_tab
        
        # Apply advanced stealth techniques
        await self._apply_stealth_techniques()
        
        print("✅ Stealth browser started successfully")
    
    async def _apply_stealth_techniques(self):
        """Apply advanced stealth techniques via CDP."""
        
        # Remove webdriver property
        await self.tab.send(cdp.runtime.evaluate(
            expression="""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            """
        ))
        
        # Override permissions
        await self.tab.send(cdp.runtime.evaluate(
            expression="""
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            """
        ))
        
        # Mask automation indicators
        await self.tab.send(cdp.runtime.evaluate(
            expression="""
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            """
        ))
        
        print("🛡️ Advanced stealth techniques applied")
    
    async def load_test_page(self):
        """Load the AI testing page."""
        test_page_path = Path(__file__).parent / "test_page.html"
        file_url = f"file://{test_page_path.absolute()}"
        
        print(f"📄 Loading test page: {file_url}")
        await self.tab.get(file_url)
        
        # Wait for page to load
        await self.tab.sleep(2)
        
        # Get viewport dimensions
        try:
            viewport_info = await self.tab.send(cdp.runtime.evaluate(
                expression="({width: window.innerWidth, height: window.innerHeight})"
            ))

            if hasattr(viewport_info, 'result') and viewport_info.result and hasattr(viewport_info.result, 'value'):
                self.viewport_width = viewport_info.result.value.get('width', 1920)
                self.viewport_height = viewport_info.result.value.get('height', 1080)
            else:
                # Fallback to default values
                self.viewport_width = 1920
                self.viewport_height = 1080
        except Exception as e:
            print(f"⚠️ Could not get viewport info: {e}")
            self.viewport_width = 1920
            self.viewport_height = 1080
        
        print(f"📐 Viewport: {self.viewport_width} x {self.viewport_height}")
        print("✅ Test page loaded successfully")
    
    async def ai_move_mouse(self):
        """Generate AI-powered mouse movement to random location."""
        print("🖱️ Generating AI mouse movement...")
        
        # Set AI active indicator
        await self.tab.send(cdp.runtime.evaluate(
            expression="window.setAIActive(true)"
        ))
        
        # Generate random target within viewport
        target_x = random.randint(50, self.viewport_width - 50)
        target_y = random.randint(50, self.viewport_height - 50)
        
        # Generate AI trajectory
        duration = random.uniform(0.8, 2.0)
        trajectory = self.ai_generator.generate_mouse_trajectory(
            self.current_mouse_x, self.current_mouse_y,
            target_x, target_y, duration
        )
        
        print(f"🎯 Moving from ({self.current_mouse_x}, {self.current_mouse_y}) to ({target_x}, {target_y})")
        print(f"📊 Generated {len(trajectory)} trajectory points over {duration:.2f}s")
        
        # Execute trajectory
        start_time = time.time()
        
        for i, (x, y, timestamp) in enumerate(trajectory):
            # Calculate actual timing
            target_time = start_time + timestamp
            current_time = time.time()
            
            if target_time > current_time:
                await asyncio.sleep(target_time - current_time)
            
            # Send mouse move event via CDP
            await self.tab.send(cdp.input_.dispatch_mouse_event(
                type_='mouseMoved',
                x=x,
                y=y
            ))
            
            # Update current position
            self.current_mouse_x = x
            self.current_mouse_y = y
            
            # Add micro-delays for realism
            if i % 3 == 0:  # Every 3rd point
                await asyncio.sleep(random.uniform(0.001, 0.005))
        
        # Clear AI active indicator
        await self.tab.send(cdp.runtime.evaluate(
            expression="window.setAIActive(false)"
        ))
        
        print(f"✅ Mouse movement completed to ({self.current_mouse_x}, {self.current_mouse_y})")
    
    async def ai_type_text(self, text: str):
        """Generate AI-powered human-like typing."""
        print(f"⌨️ Generating AI typing for: '{text}'")
        
        # Set AI active indicator
        await self.tab.send(cdp.runtime.evaluate(
            expression="window.setAIActive(true)"
        ))
        
        # Focus on text input
        await self.tab.send(cdp.runtime.evaluate(
            expression="window.getTextInput().focus()"
        ))
        
        # Clear existing text
        await self.tab.send(cdp.runtime.evaluate(
            expression="window.getTextInput().value = ''"
        ))
        
        # Generate typing pattern
        typing_pattern = self.ai_generator.generate_typing_pattern(text)
        
        print(f"📊 Generated {len(typing_pattern)} typing events")
        
        # Execute typing pattern
        start_time = time.time()
        
        for char, timestamp in typing_pattern:
            # Calculate timing
            target_time = start_time + timestamp
            current_time = time.time()
            
            if target_time > current_time:
                await asyncio.sleep(target_time - current_time)
            
            if char == '\b':  # Backspace
                await self.tab.send(cdp.input_.dispatch_key_event(
                    type_='keyDown',
                    key='Backspace'
                ))
                await self.tab.send(cdp.input_.dispatch_key_event(
                    type_='keyUp',
                    key='Backspace'
                ))
            else:
                # Send character
                await self.tab.send(cdp.input_.dispatch_key_event(
                    type_='char',
                    text=char
                ))
            
            # Add micro-variations
            await asyncio.sleep(random.uniform(0.001, 0.003))
        
        # Clear AI active indicator
        await self.tab.send(cdp.runtime.evaluate(
            expression="window.setAIActive(false)"
        ))
        
        print(f"✅ Typing completed: '{text}'")
    
    async def ai_scroll(self):
        """Generate AI-powered scrolling behavior."""
        print("📜 Generating AI scrolling...")
        
        # Set AI active indicator
        await self.tab.send(cdp.runtime.evaluate(
            expression="window.setAIActive(true)"
        ))
        
        # Get scrollable area info
        scroll_info = await self.tab.send(cdp.runtime.evaluate(
            expression="""
            (() => {
                const area = window.getScrollableArea();
                return {
                    scrollTop: area.scrollTop,
                    scrollHeight: area.scrollHeight,
                    clientHeight: area.clientHeight,
                    maxScroll: area.scrollHeight - area.clientHeight
                };
            })()
            """
        ))
        
        if not scroll_info.result or not scroll_info.result.value:
            print("❌ Could not get scroll information")
            return
        
        scroll_data = scroll_info.result.value
        current_scroll = scroll_data['scrollTop']
        max_scroll = scroll_data['maxScroll']
        
        # Generate random target scroll position
        target_scroll = random.randint(0, max(0, max_scroll))
        
        # Generate scroll pattern
        duration = random.uniform(1.5, 3.0)
        scroll_pattern = self.ai_generator.generate_scroll_pattern(
            target_scroll, current_scroll, duration
        )
        
        print(f"🎯 Scrolling from {current_scroll} to {target_scroll}")
        print(f"📊 Generated {len(scroll_pattern)} scroll events over {duration:.2f}s")
        
        # Execute scroll pattern
        start_time = time.time()
        
        for scroll_pos, timestamp in scroll_pattern:
            # Calculate timing
            target_time = start_time + timestamp
            current_time = time.time()
            
            if target_time > current_time:
                await asyncio.sleep(target_time - current_time)
            
            # Execute scroll via JavaScript
            await self.tab.send(cdp.runtime.evaluate(
                expression=f"window.getScrollableArea().scrollTop = {scroll_pos}"
            ))
            
            # Add micro-variations for realism
            await asyncio.sleep(random.uniform(0.005, 0.015))
        
        # Clear AI active indicator
        await self.tab.send(cdp.runtime.evaluate(
            expression="window.setAIActive(false)"
        ))
        
        print(f"✅ Scrolling completed to position {target_scroll}")
    
    async def close(self):
        """Close the browser."""
        if self.browser:
            try:
                await self.browser.stop()
                print("🔒 Browser closed")
            except Exception as e:
                print(f"⚠️ Browser close error: {e}")
        else:
            print("🔒 Browser was not started")


async def main():
    """Main automation interface."""
    print("🤖 AI Human Behavior Automation System")
    print("=" * 50)
    
    if not NODRIVER_AVAILABLE:
        print("❌ NoDriver is required. Install with: pip install nodriver")
        return
    
    # Check for trained model
    model_paths = [
        "../human_behavior_ai/experiments/real_data_test_fixed/final_model",
        "../human_behavior_ai/experiments/final_test/final_model",
        "../human_behavior_ai/experiments/memory_optimized/final_model"
    ]
    
    model_path = None
    for path in model_paths:
        if Path(path).exists():
            model_path = path
            break
    
    if model_path:
        print(f"🧠 Using trained AI model: {model_path}")
    else:
        print("⚠️ No trained model found, using mathematical simulation")
    
    # Initialize automation
    automation = AIAutomation(model_path)
    
    try:
        # Start browser and load test page
        await automation.start_browser()
        await automation.load_test_page()
        
        print("\n🎮 AI Automation Ready!")
        print("Commands:")
        print("  move     - AI mouse movement to random location")
        print("  type <text> - AI human-like typing")
        print("  scroll   - AI scrolling behavior")
        print("  quit     - Exit the program")
        print()
        
        # Command loop
        while True:
            try:
                command = input("🤖 Enter command: ").strip()
                
                if command.lower() in ['quit', 'exit', 'q']:
                    break
                elif command.lower() == 'move':
                    await automation.ai_move_mouse()
                elif command.lower().startswith('type '):
                    text = command[5:]  # Remove 'type ' prefix
                    if text:
                        await automation.ai_type_text(text)
                    else:
                        print("❌ Please provide text to type: type <text>")
                elif command.lower() == 'scroll':
                    await automation.ai_scroll()
                elif command.lower() == 'help':
                    print("Commands: move, type <text>, scroll, quit")
                else:
                    print("❌ Unknown command. Type 'help' for available commands.")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Error executing command: {e}")
    
    except Exception as e:
        print(f"❌ Automation error: {e}")
    
    finally:
        await automation.close()
        print("👋 AI Automation System shutdown complete")


if __name__ == "__main__":
    asyncio.run(main())
