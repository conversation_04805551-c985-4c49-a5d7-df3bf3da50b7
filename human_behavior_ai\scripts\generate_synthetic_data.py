#!/usr/bin/env python3
"""
Synthetic Training Data Generator
Creates realistic mouse movement, typing, and scrolling data to supplement training.
"""

import json
import numpy as np
import random
import math
from pathlib import Path
from typing import List, Dict, Tuple
import argparse
import uuid
from datetime import datetime


class SyntheticDataGenerator:
    """Generates realistic human behavior data for training."""
    
    def __init__(self, output_dir: str):
        """Initialize the generator."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Screen dimensions (typical laptop)
        self.screen_width = 1536
        self.screen_height = 864
        self.viewport_width = 1536
        self.viewport_height = 695
    
    def generate_natural_trajectory(self, start_x: int, start_y: int, 
                                  end_x: int, end_y: int, 
                                  duration_ms: int = 1000,
                                  frequency_hz: int = 144) -> List[Dict]:
        """Generate a natural mouse movement trajectory between two points."""
        
        # Calculate number of points based on frequency
        num_points = int((duration_ms / 1000) * frequency_hz)
        
        # Generate control points for natural curved movement
        control_points = self._generate_control_points(start_x, start_y, end_x, end_y)
        
        events = []
        base_timestamp = int(datetime.now().timestamp() * 1000)
        
        for i in range(num_points):
            t = i / (num_points - 1) if num_points > 1 else 0
            
            # Apply easing function for natural acceleration/deceleration
            eased_t = self._ease_in_out_cubic(t)
            
            # Calculate position using Bezier curve
            x, y = self._bezier_curve(control_points, eased_t)
            
            # Add natural noise and micro-movements
            noise_x = random.gauss(0, 1.5)
            noise_y = random.gauss(0, 1.5)
            x += noise_x
            y += noise_y
            
            # Ensure coordinates stay within screen bounds
            x = max(0, min(self.screen_width, x))
            y = max(0, min(self.screen_height, y))
            
            # Calculate velocity
            if i > 0:
                prev_event = events[-1]
                prev_pos = prev_event['data']['position']
                dt = (duration_ms / num_points) / 1000  # seconds
                
                vx = (x - prev_pos['x']) / dt
                vy = (y - prev_pos['y']) / dt
                vmag = math.sqrt(vx*vx + vy*vy)
            else:
                vx = vy = vmag = 0
            
            # Create event
            event = {
                'type': 'mouse_position',
                'timestamp': base_timestamp + int(i * (duration_ms / num_points)),
                'data': {
                    'position': {'x': int(x), 'y': int(y)},
                    'velocity': {'x': vx, 'y': vy, 'magnitude': vmag},
                    'deltaTime': (duration_ms / num_points) / 1000,
                    'taskId': 1  # Mouse movement task
                }
            }
            events.append(event)
        
        return events
    
    def _generate_control_points(self, start_x: int, start_y: int, 
                               end_x: int, end_y: int) -> List[Tuple[float, float]]:
        """Generate control points for natural curved movement."""
        
        # Calculate midpoint
        mid_x = (start_x + end_x) / 2
        mid_y = (start_y + end_y) / 2
        
        # Add natural curve by offsetting control points
        distance = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
        curve_intensity = min(100, distance * 0.15)
        
        # Perpendicular offset for natural curve
        dx = end_x - start_x
        dy = end_y - start_y
        
        if distance > 0:
            perp_x = -dy / distance * curve_intensity * random.uniform(-1, 1)
            perp_y = dx / distance * curve_intensity * random.uniform(-1, 1)
        else:
            perp_x = perp_y = 0
        
        control1_x = start_x + (mid_x - start_x) * 0.4 + perp_x * 0.5
        control1_y = start_y + (mid_y - start_y) * 0.4 + perp_y * 0.5
        
        control2_x = mid_x + (end_x - mid_x) * 0.6 + perp_x * 0.5
        control2_y = mid_y + (end_y - mid_y) * 0.6 + perp_y * 0.5
        
        return [
            (start_x, start_y),
            (control1_x, control1_y),
            (control2_x, control2_y),
            (end_x, end_y)
        ]
    
    def _bezier_curve(self, points: List[Tuple[float, float]], t: float) -> Tuple[float, float]:
        """Calculate point on cubic Bezier curve."""
        p0, p1, p2, p3 = points
        
        x = (1-t)**3 * p0[0] + 3*(1-t)**2*t * p1[0] + 3*(1-t)*t**2 * p2[0] + t**3 * p3[0]
        y = (1-t)**3 * p0[1] + 3*(1-t)**2*t * p1[1] + 3*(1-t)*t**2 * p2[1] + t**3 * p3[1]
        
        return x, y
    
    def _ease_in_out_cubic(self, t: float) -> float:
        """Cubic easing function for natural acceleration/deceleration."""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2
    
    def generate_typing_sequence(self, text: str, base_timestamp: int = None) -> List[Dict]:
        """Generate realistic typing events for given text."""
        if base_timestamp is None:
            base_timestamp = int(datetime.now().timestamp() * 1000)
        
        events = []
        current_time = base_timestamp
        
        # Typing speed parameters (realistic human typing)
        base_delay_ms = 120  # Base delay between keystrokes
        
        for i, char in enumerate(text):
            # Character-specific delays
            if char == ' ':
                delay = base_delay_ms * random.uniform(2.0, 4.0)  # Longer pause for spaces
            elif char in '.,!?;:':
                delay = base_delay_ms * random.uniform(1.5, 2.5)  # Pause for punctuation
            elif char.isupper():
                delay = base_delay_ms * random.uniform(1.2, 1.8)  # Slight delay for capitals
            else:
                delay = base_delay_ms * random.uniform(0.7, 1.3)  # Normal variation
            
            # Add typing rhythm variations
            if i > 0 and i % random.randint(8, 20) == 0:
                delay *= random.uniform(2.0, 3.5)  # Occasional longer pauses
            
            # Simulate occasional typos and corrections (2% chance)
            if random.random() < 0.02 and char.isalpha():
                # Add wrong character
                wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                events.append({
                    'type': 'key_down',
                    'timestamp': current_time,
                    'data': {
                        'key': wrong_char,
                        'code': f'Key{wrong_char.upper()}',
                        'taskId': 2  # Typing task
                    }
                })
                current_time += delay * 0.3
                
                # Add backspace
                events.append({
                    'type': 'key_down',
                    'timestamp': current_time,
                    'data': {
                        'key': 'Backspace',
                        'code': 'Backspace',
                        'taskId': 2
                    }
                })
                current_time += delay * 0.4
            
            # Add correct character
            events.append({
                'type': 'key_down',
                'timestamp': current_time,
                'data': {
                    'key': char,
                    'code': f'Key{char.upper()}' if char.isalpha() else char,
                    'taskId': 2
                }
            })
            
            current_time += delay
        
        return events
    
    def generate_scrolling_sequence(self, scroll_distance: int = 1000, 
                                  position_x: int = None, position_y: int = None,
                                  base_timestamp: int = None) -> List[Dict]:
        """Generate realistic scrolling events."""
        if base_timestamp is None:
            base_timestamp = int(datetime.now().timestamp() * 1000)
        
        if position_x is None:
            position_x = random.randint(200, self.screen_width - 200)
        if position_y is None:
            position_y = random.randint(200, self.screen_height - 200)
        
        events = []
        current_time = base_timestamp
        current_scroll = 0
        
        # Generate natural scrolling pattern
        while abs(current_scroll) < abs(scroll_distance):
            # Variable scroll amounts (realistic wheel behavior)
            scroll_amount = random.choice([-3, -5, -7, -8, -10, -12]) if scroll_distance < 0 else random.choice([3, 5, 7, 8, 10, 12])
            
            # Don't overshoot target
            remaining = scroll_distance - current_scroll
            if abs(scroll_amount) > abs(remaining):
                scroll_amount = remaining
            
            events.append({
                'type': 'wheel',
                'timestamp': current_time,
                'data': {
                    'deltaX': 0,
                    'deltaY': scroll_amount,
                    'deltaZ': 0,
                    'deltaMode': 0,
                    'x': position_x,
                    'y': position_y,
                    'taskId': 3  # Scrolling task
                }
            })
            
            current_scroll += scroll_amount
            
            # Natural pause between scroll events
            pause = random.uniform(50, 200)  # 50-200ms between scrolls
            current_time += pause
            
            # Occasional longer pauses (reading behavior)
            if random.random() < 0.1:
                current_time += random.uniform(500, 1500)
        
        return events
    
    def generate_complete_session(self, session_duration_minutes: int = 5) -> Dict:
        """Generate a complete synthetic session with multiple task types."""
        session_id = str(uuid.uuid4())
        base_timestamp = int(datetime.now().timestamp() * 1000)
        
        all_events = []
        current_time = base_timestamp
        
        # Generate diverse mouse movements (40% of session)
        movement_duration = int(session_duration_minutes * 60 * 1000 * 0.4)
        movement_end_time = current_time + movement_duration
        
        while current_time < movement_end_time:
            # Random start and end points
            start_x = random.randint(50, self.screen_width - 50)
            start_y = random.randint(50, self.screen_height - 50)
            end_x = random.randint(50, self.screen_width - 50)
            end_y = random.randint(50, self.screen_height - 50)
            
            # Generate trajectory
            trajectory_duration = random.randint(800, 2500)
            trajectory_events = self.generate_natural_trajectory(
                start_x, start_y, end_x, end_y, trajectory_duration
            )
            
            # Adjust timestamps
            for event in trajectory_events:
                event['timestamp'] = current_time + (event['timestamp'] - trajectory_events[0]['timestamp'])
            
            all_events.extend(trajectory_events)
            current_time += trajectory_duration
            
            # Add pause between movements
            pause = random.randint(200, 1000)
            current_time += pause
        
        # Generate typing sequences (30% of session)
        typing_texts = [
            "The quick brown fox jumps over the lazy dog.",
            "Hello world! This is a test of natural typing patterns.",
            "Machine learning models require diverse training data.",
            "Human behavior simulation is complex and nuanced.",
            "Artificial intelligence can learn from human interactions."
        ]
        
        typing_duration = int(session_duration_minutes * 60 * 1000 * 0.3)
        typing_end_time = current_time + typing_duration
        
        while current_time < typing_end_time:
            text = random.choice(typing_texts)
            typing_events = self.generate_typing_sequence(text, current_time)
            all_events.extend(typing_events)
            
            if typing_events:
                current_time = typing_events[-1]['timestamp'] + random.randint(1000, 3000)
        
        # Generate scrolling sequences (30% of session)
        scrolling_duration = int(session_duration_minutes * 60 * 1000 * 0.3)
        scrolling_end_time = current_time + scrolling_duration
        
        while current_time < scrolling_end_time:
            scroll_distance = random.randint(-2000, -500)  # Scroll up
            scroll_events = self.generate_scrolling_sequence(
                scroll_distance, base_timestamp=current_time
            )
            all_events.extend(scroll_events)
            
            if scroll_events:
                current_time = scroll_events[-1]['timestamp'] + random.randint(500, 2000)
        
        # Sort all events by timestamp
        all_events.sort(key=lambda x: x['timestamp'])
        
        # Create session metadata
        session_data = {
            'id': session_id,
            'startTime': base_timestamp,
            'endTime': current_time,
            'duration': current_time - base_timestamp,
            'status': 'completed',
            'metadata': {
                'userAgent': 'SyntheticDataGenerator/1.0',
                'screenResolution': f'{self.screen_width}x{self.screen_height}',
                'viewportSize': f'{self.viewport_width}x{self.viewport_height}',
                'captureFrequency': 144,
                'synthetic': True
            },
            'dataPoints': len(all_events),
            'lastUpdate': current_time
        }
        
        return {
            'metadata': session_data,
            'events': all_events
        }
    
    def save_session(self, session_data: Dict, filename_prefix: str = "synthetic"):
        """Save session data to files."""
        session_id = session_data['metadata']['id']
        
        # Save metadata
        metadata_file = self.output_dir / f"{filename_prefix}_{session_id}.json"
        with open(metadata_file, 'w') as f:
            json.dump(session_data['metadata'], f, indent=2)
        
        # Save events data
        events_file = self.output_dir / f"{filename_prefix}_{session_id}_data.json"
        with open(events_file, 'w') as f:
            json.dump(session_data['events'], f, indent=2)
        
        print(f"✅ Saved session: {metadata_file.name}")
        print(f"   📊 Events: {len(session_data['events']):,}")
        print(f"   ⏱️ Duration: {session_data['metadata']['duration']/1000:.1f}s")
        
        return metadata_file, events_file


def main():
    """Main generation function."""
    parser = argparse.ArgumentParser(description='Generate synthetic training data')
    parser.add_argument('--output-dir', default='data/sessions', help='Output directory for generated data')
    parser.add_argument('--num-sessions', type=int, default=5, help='Number of sessions to generate')
    parser.add_argument('--duration', type=int, default=5, help='Duration of each session in minutes')
    
    args = parser.parse_args()
    
    print("🤖 Synthetic Training Data Generator")
    print("=" * 50)
    
    generator = SyntheticDataGenerator(args.output_dir)
    
    for i in range(args.num_sessions):
        print(f"\n📝 Generating session {i+1}/{args.num_sessions}...")
        
        session_data = generator.generate_complete_session(args.duration)
        generator.save_session(session_data, f"synthetic_{i+1:03d}")
    
    print(f"\n✅ Generated {args.num_sessions} synthetic sessions!")
    print(f"💾 Saved to: {args.output_dir}")


if __name__ == "__main__":
    main()
