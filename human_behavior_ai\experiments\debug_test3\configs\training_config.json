{"model_type": "hybrid", "model_config": {"hidden_dim": 512, "num_layers": 12, "num_heads": 8, "dropout": 0.1, "activation": "gelu", "input_dim": 64, "output_dim": 64, "sequence_length": 512, "mouse_dim": 16, "keyboard_dim": 16, "scroll_dim": 8, "context_dim": 24, "learning_rate": 0.0002, "weight_decay": 1e-05, "gradient_clip": 1.0, "use_positional_encoding": true, "use_layer_norm": true, "use_residual_connections": true, "use_attention_dropout": true, "model_specific": {}}, "training_args": {"model_type": "hybrid", "config": null, "data_path": "data/sessions", "val_data_path": null, "sequence_length": 512, "batch_size": 4, "val_batch_size": 64, "epochs": 2, "learning_rate": 0.0002, "weight_decay": 1e-05, "warmup_steps": 1000, "gradient_clip": 1.0, "training_strategy": "curriculum", "mixed_precision": true, "gradient_accumulation": 32, "distributed": false, "local_rank": 0, "output_dir": "experiments/debug_test3", "save_every": 1000, "eval_every": 500, "log_every": 100, "wandb_project": "human-behavior-ai", "wandb_run_name": null, "no_wandb": true, "num_workers": 0, "pin_memory": false, "compile": false, "debug": false, "profile": false, "seed": 42}, "device_info": {"platform": "Windows-11-10.0.26200-SP0", "python_version": "3.12.3", "pytorch_version": "2.7.1+cu128", "cpu_count": 8, "memory_total_gb": 13.86, "cuda_available": true, "cuda_version": "12.8", "cudnn_version": 90701, "cuda_device_count": 1, "cuda_devices": [{"device_id": 0, "name": "NVIDIA GeForce RTX 2050", "memory_total_gb": 4.0, "compute_capability": "8.6", "multiprocessor_count": 16}]}}