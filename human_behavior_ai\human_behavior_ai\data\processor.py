"""
Data processing and feature extraction for behavioral data.

This module handles the preprocessing of raw interaction data
into features suitable for machine learning models.
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import json
from pathlib import Path

from .dataset import BehaviorSequence


@dataclass
class ProcessingConfig:
    """Configuration for data processing."""
    
    # Sequence parameters
    sequence_length: int = 1000
    overlap_ratio: float = 0.5
    min_sequence_length: int = 100
    
    # Feature extraction
    extract_velocity: bool = True
    extract_acceleration: bool = True
    extract_jerk: bool = True
    extract_angles: bool = True
    extract_timing: bool = True
    
    # Normalization
    normalize_coordinates: bool = True
    normalize_timing: bool = True
    screen_width: int = 1920
    screen_height: int = 1080
    
    # Filtering
    remove_outliers: bool = True
    outlier_threshold: float = 3.0
    smooth_trajectories: bool = True
    smoothing_window: int = 5
    
    # Sampling
    target_frequency: float = 144.0  # Hz
    resample_method: str = "linear"  # linear, cubic, nearest


class DataProcessor:
    """
    Main data processor for behavioral interaction data.
    
    Handles loading, cleaning, and preprocessing of raw interaction
    data from the recording system.
    """
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.feature_extractor = FeatureExtractor(config)
        
        # Statistics for normalization
        self.coordinate_stats = {}
        self.timing_stats = {}
        self.feature_stats = {}
    
    def process_session_file(self, file_path: Union[str, Path]) -> List[BehaviorSequence]:
        """
        Process a single session file into behavior sequences.

        Args:
            file_path: Path to the session JSON file

        Returns:
            List of processed behavior sequences
        """
        # Type checking and conversion
        if isinstance(file_path, list):
            raise TypeError(f"Expected str or Path, got list: {file_path}")

        file_path = Path(file_path)

        # Check if file exists
        if not file_path.exists():
            raise FileNotFoundError(f"Session file not found: {file_path}")

        # Check if this is a metadata file or data file
        if file_path.name.endswith('_data.json'):
            # This is the actual data file
            return self._process_data_file(file_path)
        elif file_path.name.endswith('.json') and not file_path.name.endswith('_data.json'):
            # This is a metadata file, look for corresponding data file
            data_file = file_path.parent / f"{file_path.stem}_data.json"
            if data_file.exists():
                return self._process_data_file(data_file)
            else:
                # Fallback to old format
                return self._process_old_format(file_path)
        else:
            return self._process_old_format(file_path)

    def _process_data_file(self, file_path: Path) -> List[BehaviorSequence]:
        """Process the new data file format with direct event array."""
        # Load event data
        with open(file_path, 'r') as f:
            events = json.load(f)

        if not isinstance(events, list):
            return []

        # Convert events to the format expected by the processor
        interactions = []
        for event in events:
            if event.get('type') == 'mouse_position':
                data = event.get('data', {})
                position = data.get('position', {})
                velocity = data.get('velocity', {})

                interaction = {
                    'timestamp': event.get('timestamp', 0),
                    'x': position.get('x', 0),
                    'y': position.get('y', 0),
                    'velocity_x': velocity.get('x', 0),
                    'velocity_y': velocity.get('y', 0),
                    'velocity_magnitude': velocity.get('magnitude', 0),
                    'delta_time': data.get('deltaTime', 0),
                    'task_id': data.get('taskId', 0),
                    'type': 'mouse'
                }
                interactions.append(interaction)

            elif event.get('type') == 'wheel':
                data = event.get('data', {})

                interaction = {
                    'timestamp': event.get('timestamp', 0),
                    'x': data.get('x', 0),
                    'y': data.get('y', 0),
                    'delta_x': data.get('deltaX', 0),
                    'delta_y': data.get('deltaY', 0),
                    'delta_z': data.get('deltaZ', 0),
                    'delta_mode': data.get('deltaMode', 0),
                    'task_id': data.get('taskId', 0),
                    'type': 'wheel'
                }
                interactions.append(interaction)

        if not interactions:
            return []

        # Create a single task data structure
        task_data = {
            'type': 'mixed_interaction',
            'interactions': interactions
        }

        return self._process_task_data(task_data)

    def _process_old_format(self, file_path: Path) -> List[BehaviorSequence]:
        """Process the old session format with tasks structure."""
        # Load session data
        with open(file_path, 'r') as f:
            session_data = json.load(f)

        sequences = []

        # Process each task in the session
        for task_data in session_data.get('tasks', []):
            task_sequences = self._process_task_data(task_data)
            sequences.extend(task_sequences)

        return sequences
    
    def _process_task_data(self, task_data: Dict[str, Any]) -> List[BehaviorSequence]:
        """Process data from a single task."""
        task_type = task_data.get('type', 'unknown')
        interactions = task_data.get('interactions', [])
        
        if not interactions:
            return []
        
        # Convert to DataFrame for easier processing
        df = pd.DataFrame(interactions)
        
        # Clean and filter data
        df = self._clean_interaction_data(df)
        
        # Extract features
        features = self.feature_extractor.extract_features(df)
        
        # Create sequences
        sequences = self._create_sequences(features, task_type, task_data)
        
        return sequences
    
    def _clean_interaction_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and filter interaction data."""
        # Remove invalid entries
        df = df.dropna(subset=['timestamp', 'x', 'y'])
        
        # Sort by timestamp
        df = df.sort_values('timestamp')
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['timestamp'])
        
        # Filter outliers
        if self.config.remove_outliers:
            df = self._remove_outliers(df)
        
        # Smooth trajectories
        if self.config.smooth_trajectories:
            df = self._smooth_trajectories(df)
        
        # Resample to target frequency
        df = self._resample_data(df)
        
        return df
    
    def _remove_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove outlier points based on movement characteristics."""
        # Calculate movement speed
        df['dx'] = df['x'].diff()
        df['dy'] = df['y'].diff()
        df['dt'] = df['timestamp'].diff()
        df['speed'] = np.sqrt(df['dx']**2 + df['dy']**2) / (df['dt'] + 1e-6)
        
        # Remove points with unrealistic speeds
        speed_threshold = df['speed'].quantile(0.95) + self.config.outlier_threshold * df['speed'].std()
        df = df[df['speed'] <= speed_threshold]
        
        # Remove coordinate outliers
        for coord in ['x', 'y']:
            mean_val = df[coord].mean()
            std_val = df[coord].std()
            threshold = self.config.outlier_threshold * std_val
            df = df[abs(df[coord] - mean_val) <= threshold]
        
        return df.drop(['dx', 'dy', 'dt', 'speed'], axis=1)
    
    def _smooth_trajectories(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply smoothing to trajectories."""
        window = self.config.smoothing_window
        
        # Apply rolling average to coordinates
        df['x'] = df['x'].rolling(window=window, center=True, min_periods=1).mean()
        df['y'] = df['y'].rolling(window=window, center=True, min_periods=1).mean()
        
        return df
    
    def _resample_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Resample data to target frequency."""
        if len(df) < 2:
            return df
        
        # Calculate current frequency
        time_diff = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]) / 1000.0  # Convert to seconds
        current_freq = len(df) / time_diff if time_diff > 0 else self.config.target_frequency
        
        # Skip resampling if already close to target
        if abs(current_freq - self.config.target_frequency) < 5:
            return df
        
        # Create target timestamps
        start_time = df['timestamp'].iloc[0]
        end_time = df['timestamp'].iloc[-1]
        target_interval = 1000.0 / self.config.target_frequency  # milliseconds
        
        target_timestamps = np.arange(start_time, end_time, target_interval)
        
        # Interpolate coordinates
        resampled_data = []
        for timestamp in target_timestamps:
            # Find nearest points for interpolation
            idx = np.searchsorted(df['timestamp'], timestamp)
            
            if idx == 0:
                row = df.iloc[0].copy()
            elif idx >= len(df):
                row = df.iloc[-1].copy()
            else:
                # Linear interpolation
                t1, t2 = df['timestamp'].iloc[idx-1], df['timestamp'].iloc[idx]
                alpha = (timestamp - t1) / (t2 - t1) if t2 != t1 else 0
                
                row = df.iloc[idx-1].copy()
                for col in ['x', 'y']:
                    v1, v2 = df[col].iloc[idx-1], df[col].iloc[idx]
                    row[col] = v1 + alpha * (v2 - v1)
            
            row['timestamp'] = timestamp
            resampled_data.append(row)
        
        return pd.DataFrame(resampled_data)
    
    def _create_sequences(self, features: np.ndarray, task_type: str, 
                         task_data: Dict[str, Any]) -> List[BehaviorSequence]:
        """Create behavior sequences from features."""
        sequences = []
        
        # Calculate sequence parameters
        seq_len = self.config.sequence_length
        overlap = int(seq_len * self.config.overlap_ratio)
        step_size = seq_len - overlap
        
        # Create overlapping sequences
        for start_idx in range(0, len(features) - self.config.min_sequence_length, step_size):
            end_idx = min(start_idx + seq_len, len(features))
            
            if end_idx - start_idx < self.config.min_sequence_length:
                break
            
            sequence_features = features[start_idx:end_idx]
            
            # Extract timestamps for this sequence
            interactions = task_data.get('interactions', [])
            sequence_timestamps = np.array([
                interactions[i].get('timestamp', i) for i in range(start_idx, min(end_idx, len(interactions)))
            ])

            # Pad timestamps if needed
            if len(sequence_timestamps) < len(sequence_features):
                last_timestamp = sequence_timestamps[-1] if len(sequence_timestamps) > 0 else 0
                padding = np.arange(last_timestamp + 1, last_timestamp + 1 + (len(sequence_features) - len(sequence_timestamps)))
                sequence_timestamps = np.concatenate([sequence_timestamps, padding])

            # Split features into different modalities
            # Assuming features are structured as [mouse_features, scroll_features, context_features]
            feature_dim = sequence_features.shape[1]
            mouse_dim = min(6, feature_dim)  # x, y, vx, vy, vmag, dt
            scroll_dim = min(4, max(0, feature_dim - mouse_dim))  # dx, dy, dz, mode
            context_dim = max(0, feature_dim - mouse_dim - scroll_dim)

            seq_len_actual = len(sequence_features)
            mouse_data = sequence_features[:, :mouse_dim] if mouse_dim > 0 else np.zeros((seq_len_actual, 2))
            scroll_data = sequence_features[:, mouse_dim:mouse_dim+scroll_dim] if scroll_dim > 0 else np.zeros((seq_len_actual, 4))
            context_data = sequence_features[:, mouse_dim+scroll_dim:] if context_dim > 0 else np.zeros((seq_len_actual, 1))
            keyboard_data = np.zeros((seq_len_actual, 1))  # No keyboard data in current format

            # Create behavior sequence
            sequence = BehaviorSequence(
                mouse_data=mouse_data,
                keyboard_data=keyboard_data,
                scroll_data=scroll_data,
                context_data=context_data,
                timestamps=sequence_timestamps[:seq_len_actual],
                metadata={
                    'start_idx': start_idx,
                    'end_idx': end_idx,
                    'task_id': task_data.get('id', ''),
                    'duration': task_data.get('duration', 0),
                    'completion_time': task_data.get('completionTime', 0)
                }
            )
            
            sequences.append(sequence)
        
        return sequences
    
    def compute_normalization_stats(self, sequences: List[BehaviorSequence]):
        """Compute statistics for data normalization."""
        # Collect all data from sequences
        all_mouse_data = []
        all_scroll_data = []
        all_context_data = []

        for seq in sequences:
            all_mouse_data.append(torch.from_numpy(seq.mouse_data).float())
            all_scroll_data.append(torch.from_numpy(seq.scroll_data).float())
            all_context_data.append(torch.from_numpy(seq.context_data).float())

        # Concatenate all data
        all_mouse = torch.cat(all_mouse_data, dim=0)
        all_scroll = torch.cat(all_scroll_data, dim=0)
        all_context = torch.cat(all_context_data, dim=0)

        self.feature_stats = {
            'mouse': {
                'mean': all_mouse.mean(dim=0),
                'std': all_mouse.std(dim=0),
                'min': all_mouse.min(dim=0)[0],
                'max': all_mouse.max(dim=0)[0]
            },
            'scroll': {
                'mean': all_scroll.mean(dim=0),
                'std': all_scroll.std(dim=0),
                'min': all_scroll.min(dim=0)[0],
                'max': all_scroll.max(dim=0)[0]
            },
            'context': {
                'mean': all_context.mean(dim=0),
                'std': all_context.std(dim=0),
                'min': all_context.min(dim=0)[0],
                'max': all_context.max(dim=0)[0]
            }
        }
    
    def normalize_sequences(self, sequences: List[BehaviorSequence]) -> List[BehaviorSequence]:
        """Normalize sequences using computed statistics."""
        if not self.feature_stats:
            self.compute_normalization_stats(sequences)

        normalized_sequences = []
        for sequence in sequences:
            # Normalize each modality separately
            mouse_data = torch.from_numpy(sequence.mouse_data).float()
            scroll_data = torch.from_numpy(sequence.scroll_data).float()
            context_data = torch.from_numpy(sequence.context_data).float()

            normalized_mouse = (mouse_data - self.feature_stats['mouse']['mean']) / (self.feature_stats['mouse']['std'] + 1e-8)
            normalized_scroll = (scroll_data - self.feature_stats['scroll']['mean']) / (self.feature_stats['scroll']['std'] + 1e-8)
            normalized_context = (context_data - self.feature_stats['context']['mean']) / (self.feature_stats['context']['std'] + 1e-8)

            normalized_sequence = BehaviorSequence(
                mouse_data=normalized_mouse.numpy(),
                keyboard_data=sequence.keyboard_data,  # Keep keyboard data as is
                scroll_data=normalized_scroll.numpy(),
                context_data=normalized_context.numpy(),
                timestamps=sequence.timestamps,
                metadata=sequence.metadata
            )
            normalized_sequences.append(normalized_sequence)

        return normalized_sequences


class FeatureExtractor:
    """
    Feature extractor for behavioral interaction data.
    
    Extracts various features from raw coordinate and timing data
    including kinematic features, patterns, and statistical measures.
    """
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
    
    def extract_features(self, df: pd.DataFrame) -> np.ndarray:
        """
        Extract comprehensive features from interaction data.
        
        Args:
            df: DataFrame with columns ['timestamp', 'x', 'y', 'type']
            
        Returns:
            Feature array of shape (n_samples, n_features)
        """
        features = []
        
        # Basic coordinates (normalized if configured)
        if self.config.normalize_coordinates:
            x_norm = df['x'] / self.config.screen_width
            y_norm = df['y'] / self.config.screen_height
        else:
            x_norm = df['x']
            y_norm = df['y']
        
        features.extend([x_norm.values, y_norm.values])
        
        # Timing features
        if self.config.extract_timing:
            timing_features = self._extract_timing_features(df)
            features.extend(timing_features)
        
        # Kinematic features
        if self.config.extract_velocity:
            velocity_features = self._extract_velocity_features(df)
            features.extend(velocity_features)
        
        if self.config.extract_acceleration:
            acceleration_features = self._extract_acceleration_features(df)
            features.extend(acceleration_features)
        
        if self.config.extract_jerk:
            jerk_features = self._extract_jerk_features(df)
            features.extend(jerk_features)
        
        if self.config.extract_angles:
            angle_features = self._extract_angle_features(df)
            features.extend(angle_features)
        
        # Stack features
        feature_array = np.column_stack(features)
        
        return feature_array
    
    def _extract_timing_features(self, df: pd.DataFrame) -> List[np.ndarray]:
        """Extract timing-related features."""
        # Time differences
        dt = df['timestamp'].diff().fillna(0).values
        
        # Normalized timing
        if self.config.normalize_timing:
            dt_norm = dt / 1000.0  # Convert to seconds
        else:
            dt_norm = dt
        
        return [dt_norm]
    
    def _extract_velocity_features(self, df: pd.DataFrame) -> List[np.ndarray]:
        """Extract velocity features."""
        # Position differences
        dx = df['x'].diff().fillna(0).values
        dy = df['y'].diff().fillna(0).values
        dt = df['timestamp'].diff().fillna(1).values + 1e-6  # Avoid division by zero
        
        # Velocity components
        vx = dx / dt
        vy = dy / dt
        
        # Velocity magnitude
        v_mag = np.sqrt(vx**2 + vy**2)
        
        return [vx, vy, v_mag]
    
    def _extract_acceleration_features(self, df: pd.DataFrame) -> List[np.ndarray]:
        """Extract acceleration features."""
        # First compute velocity
        dx = df['x'].diff().fillna(0).values
        dy = df['y'].diff().fillna(0).values
        dt = df['timestamp'].diff().fillna(1).values + 1e-6
        
        vx = dx / dt
        vy = dy / dt
        
        # Acceleration components
        dvx = np.diff(vx, prepend=vx[0])
        dvy = np.diff(vy, prepend=vy[0])
        
        ax = dvx / dt
        ay = dvy / dt
        
        # Acceleration magnitude
        a_mag = np.sqrt(ax**2 + ay**2)
        
        return [ax, ay, a_mag]
    
    def _extract_jerk_features(self, df: pd.DataFrame) -> List[np.ndarray]:
        """Extract jerk (rate of change of acceleration) features."""
        # Compute acceleration first
        dx = df['x'].diff().fillna(0).values
        dy = df['y'].diff().fillna(0).values
        dt = df['timestamp'].diff().fillna(1).values + 1e-6
        
        vx = dx / dt
        vy = dy / dt
        
        dvx = np.diff(vx, prepend=vx[0])
        dvy = np.diff(vy, prepend=vy[0])
        
        ax = dvx / dt
        ay = dvy / dt
        
        # Jerk components
        dax = np.diff(ax, prepend=ax[0])
        day = np.diff(ay, prepend=ay[0])
        
        jx = dax / dt
        jy = day / dt
        
        # Jerk magnitude
        j_mag = np.sqrt(jx**2 + jy**2)
        
        return [jx, jy, j_mag]
    
    def _extract_angle_features(self, df: pd.DataFrame) -> List[np.ndarray]:
        """Extract angle and direction features."""
        # Movement direction
        dx = df['x'].diff().fillna(0).values
        dy = df['y'].diff().fillna(0).values
        
        # Angle of movement
        angles = np.arctan2(dy, dx)
        
        # Angular velocity
        angle_diff = np.diff(angles, prepend=angles[0])
        dt = df['timestamp'].diff().fillna(1).values + 1e-6
        angular_velocity = angle_diff / dt
        
        return [angles, angular_velocity]
