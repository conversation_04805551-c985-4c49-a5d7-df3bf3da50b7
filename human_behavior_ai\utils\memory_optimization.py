"""
Memory optimization utilities for training on low-memory GPUs.
"""

import os
import gc
import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


def setup_cuda_memory_optimization(memory_fraction: float = 0.85):
    """
    Setup CUDA memory optimizations for low-memory training.
    
    Args:
        memory_fraction: Fraction of GPU memory to use (default: 0.85)
    """
    if not torch.cuda.is_available():
        return
    
    # Set memory fraction
    torch.cuda.set_per_process_memory_fraction(memory_fraction)
    
    # Enable memory efficient attention backends
    torch.backends.cuda.enable_flash_sdp(True)
    torch.backends.cuda.enable_mem_efficient_sdp(True)
    torch.backends.cuda.enable_math_sdp(False)  # Disable to save memory
    
    # Set CUDA allocator configuration
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    
    # Clear cache
    torch.cuda.empty_cache()
    
    logger.info(f"CUDA memory optimization enabled with {memory_fraction*100}% memory fraction")


def get_memory_stats() -> Dict[str, float]:
    """Get detailed memory statistics."""
    stats = {}
    
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1e9
            reserved = torch.cuda.memory_reserved(i) / 1e9
            max_allocated = torch.cuda.max_memory_allocated(i) / 1e9
            max_reserved = torch.cuda.max_memory_reserved(i) / 1e9
            
            stats[f'cuda_{i}_allocated_gb'] = allocated
            stats[f'cuda_{i}_reserved_gb'] = reserved
            stats[f'cuda_{i}_max_allocated_gb'] = max_allocated
            stats[f'cuda_{i}_max_reserved_gb'] = max_reserved
    
    return stats


def clear_memory():
    """Clear GPU and CPU memory."""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()


def memory_efficient_attention(query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                              attn_mask: Optional[torch.Tensor] = None,
                              dropout_p: float = 0.0,
                              is_causal: bool = False,
                              chunk_size: int = 1024) -> torch.Tensor:
    """
    Memory-efficient attention implementation using chunking.
    
    Args:
        query: Query tensor [batch_size, seq_len, d_model]
        key: Key tensor [batch_size, seq_len, d_model]
        value: Value tensor [batch_size, seq_len, d_model]
        attn_mask: Optional attention mask
        dropout_p: Dropout probability
        is_causal: Whether to use causal attention
        chunk_size: Size of chunks for processing
    
    Returns:
        Attention output tensor
    """
    batch_size, seq_len, d_model = query.shape
    
    # If sequence is short enough, use standard attention
    if seq_len <= chunk_size:
        if hasattr(torch.nn.functional, 'scaled_dot_product_attention'):
            return torch.nn.functional.scaled_dot_product_attention(
                query, key, value, attn_mask=attn_mask,
                dropout_p=dropout_p, is_causal=is_causal
            )
        else:
            # Fallback to manual implementation
            scale = 1.0 / (d_model ** 0.5)
            scores = torch.matmul(query, key.transpose(-2, -1)) * scale
            
            if attn_mask is not None:
                scores = scores.masked_fill(attn_mask == 0, float('-inf'))
            
            attn_weights = torch.softmax(scores, dim=-1)
            
            if dropout_p > 0.0 and query.training:
                attn_weights = torch.dropout(attn_weights, dropout_p, train=True)
            
            return torch.matmul(attn_weights, value)
    
    # Chunked attention for long sequences
    output = torch.zeros_like(query)
    
    for i in range(0, seq_len, chunk_size):
        end_i = min(i + chunk_size, seq_len)
        query_chunk = query[:, i:end_i]
        
        # Compute attention for this chunk
        chunk_output = memory_efficient_attention(
            query_chunk, key, value,
            attn_mask=attn_mask[:, i:end_i] if attn_mask is not None else None,
            dropout_p=dropout_p,
            is_causal=is_causal,
            chunk_size=chunk_size
        )
        
        output[:, i:end_i] = chunk_output
    
    return output


def gradient_checkpointing_wrapper(module: nn.Module, *args, **kwargs):
    """
    Wrapper for gradient checkpointing to save memory during backpropagation.
    """
    if module.training:
        return torch.utils.checkpoint.checkpoint(module, *args, **kwargs)
    else:
        return module(*args, **kwargs)


def optimize_model_for_memory(model: nn.Module, 
                            enable_gradient_checkpointing: bool = True,
                            convert_to_half: bool = False) -> nn.Module:
    """
    Apply memory optimizations to a model.
    
    Args:
        model: PyTorch model to optimize
        enable_gradient_checkpointing: Whether to enable gradient checkpointing
        convert_to_half: Whether to convert model to half precision
    
    Returns:
        Optimized model
    """
    if enable_gradient_checkpointing:
        # Enable gradient checkpointing for transformer layers
        for name, module in model.named_modules():
            if 'transformer' in name.lower() or 'attention' in name.lower():
                if hasattr(module, 'gradient_checkpointing_enable'):
                    module.gradient_checkpointing_enable()
    
    if convert_to_half:
        model = model.half()
    
    return model


def calculate_memory_requirements(batch_size: int, 
                                sequence_length: int,
                                hidden_dim: int,
                                num_layers: int,
                                num_heads: int,
                                vocab_size: int = 50000) -> Dict[str, float]:
    """
    Calculate approximate memory requirements for a transformer model.
    
    Args:
        batch_size: Batch size
        sequence_length: Sequence length
        hidden_dim: Hidden dimension
        num_layers: Number of transformer layers
        num_heads: Number of attention heads
        vocab_size: Vocabulary size
    
    Returns:
        Dictionary with memory requirements in GB
    """
    # Model parameters (in bytes, assuming float32)
    embedding_params = vocab_size * hidden_dim * 4
    attention_params = num_layers * (4 * hidden_dim * hidden_dim) * 4  # Q, K, V, O projections
    ffn_params = num_layers * (2 * hidden_dim * 4 * hidden_dim) * 4  # Two linear layers with 4x expansion
    layer_norm_params = num_layers * 2 * hidden_dim * 4  # Two layer norms per layer
    
    total_params = embedding_params + attention_params + ffn_params + layer_norm_params
    
    # Activations (in bytes, assuming float32)
    # Attention scores: batch_size * num_heads * sequence_length^2
    attention_scores = batch_size * num_heads * sequence_length * sequence_length * 4
    
    # Hidden states: batch_size * sequence_length * hidden_dim * num_layers
    hidden_states = batch_size * sequence_length * hidden_dim * num_layers * 4
    
    # Gradients (same size as parameters)
    gradients = total_params
    
    # Optimizer states (Adam: 2x parameters for momentum and variance)
    optimizer_states = total_params * 2
    
    return {
        'model_parameters_gb': total_params / 1e9,
        'activations_gb': (attention_scores + hidden_states) / 1e9,
        'gradients_gb': gradients / 1e9,
        'optimizer_states_gb': optimizer_states / 1e9,
        'total_estimated_gb': (total_params + attention_scores + hidden_states + gradients + optimizer_states) / 1e9
    }


def suggest_memory_optimizations(available_memory_gb: float,
                               current_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Suggest memory optimizations based on available memory.
    
    Args:
        available_memory_gb: Available GPU memory in GB
        current_config: Current model configuration
    
    Returns:
        Suggested optimizations
    """
    suggestions = {}
    
    # Calculate current memory requirements
    memory_req = calculate_memory_requirements(
        batch_size=current_config.get('batch_size', 32),
        sequence_length=current_config.get('sequence_length', 1024),
        hidden_dim=current_config.get('hidden_dim', 512),
        num_layers=current_config.get('num_layers', 12),
        num_heads=current_config.get('num_heads', 8)
    )
    
    estimated_usage = memory_req['total_estimated_gb']
    
    if estimated_usage > available_memory_gb * 0.8:  # Leave 20% headroom
        # Suggest reductions
        if current_config.get('batch_size', 32) > 4:
            suggestions['batch_size'] = max(2, current_config.get('batch_size', 32) // 2)
        
        if current_config.get('sequence_length', 1024) > 256:
            suggestions['sequence_length'] = max(256, current_config.get('sequence_length', 1024) // 2)
        
        if current_config.get('hidden_dim', 512) > 256:
            suggestions['hidden_dim'] = max(256, current_config.get('hidden_dim', 512) // 2)
        
        if current_config.get('num_layers', 12) > 6:
            suggestions['num_layers'] = max(6, current_config.get('num_layers', 12) // 2)
        
        # Suggest memory optimization techniques
        suggestions['enable_gradient_checkpointing'] = True
        suggestions['use_mixed_precision'] = True
        suggestions['increase_gradient_accumulation'] = True
        suggestions['reduce_num_workers'] = True
        suggestions['disable_pin_memory'] = True
    
    return suggestions


def monitor_memory_usage(func):
    """Decorator to monitor memory usage of a function."""
    def wrapper(*args, **kwargs):
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()
            start_memory = torch.cuda.memory_allocated()
        
        result = func(*args, **kwargs)
        
        if torch.cuda.is_available():
            end_memory = torch.cuda.memory_allocated()
            peak_memory = torch.cuda.max_memory_allocated()
            
            logger.debug(f"{func.__name__} memory usage:")
            logger.debug(f"  Start: {start_memory / 1e6:.1f} MB")
            logger.debug(f"  End: {end_memory / 1e6:.1f} MB")
            logger.debug(f"  Peak: {peak_memory / 1e6:.1f} MB")
            logger.debug(f"  Delta: {(end_memory - start_memory) / 1e6:.1f} MB")
        
        return result
    
    return wrapper
