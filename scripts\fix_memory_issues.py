#!/usr/bin/env python3
"""
Quick fix script to resolve CUDA out of memory issues.
This script applies immediate fixes to the existing training setup.
"""

import os
import sys
import torch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def apply_immediate_memory_fixes():
    """Apply immediate memory optimization fixes."""
    
    print("🔧 Applying immediate CUDA memory fixes...")
    
    # 1. Set CUDA memory allocator configuration
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    print("✅ Set PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True")
    
    # 2. Set memory fraction
    if torch.cuda.is_available():
        torch.cuda.set_per_process_memory_fraction(0.85)
        print("✅ Set CUDA memory fraction to 85%")
        
        # 3. Enable memory efficient attention
        torch.backends.cuda.enable_flash_sdp(True)
        torch.backends.cuda.enable_mem_efficient_sdp(True)
        torch.backends.cuda.enable_math_sdp(False)  # Disable to save memory
        print("✅ Enabled memory efficient attention backends")
        
        # 4. Clear cache
        torch.cuda.empty_cache()
        print("✅ Cleared CUDA cache")
        
        # 5. Show memory info
        device_name = torch.cuda.get_device_name()
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"📊 GPU: {device_name}")
        print(f"📊 Total memory: {total_memory:.1f} GB")
        print(f"📊 Available memory: {total_memory * 0.85:.1f} GB (85% of total)")
    
    print("\n🎯 Memory optimization recommendations:")
    print("   1. Reduce batch size to 4-8")
    print("   2. Reduce sequence length to 512")
    print("   3. Increase gradient accumulation to 16-32")
    print("   4. Use the memory-optimized training script")
    
    return True


def create_memory_optimized_command():
    """Create a memory-optimized training command."""
    
    command = """
# Memory-optimized training command for 4GB GPU:
python scripts/train_memory_optimized.py \\
    --data-path data/sessions \\
    --model-type hybrid \\
    --batch-size 4 \\
    --sequence-length 512 \\
    --hidden-dim 256 \\
    --num-layers 6 \\
    --num-heads 8 \\
    --epochs 200 \\
    --learning-rate 2e-4 \\
    --gradient-accumulation 16 \\
    --mixed-precision \\
    --gradient-checkpointing \\
    --num-workers 2 \\
    --output-dir experiments/memory_optimized \\
    --no-wandb

# Alternative: Even more aggressive memory optimization
python scripts/train_memory_optimized.py \\
    --data-path data/sessions \\
    --model-type hybrid \\
    --batch-size 2 \\
    --sequence-length 256 \\
    --hidden-dim 128 \\
    --num-layers 4 \\
    --num-heads 4 \\
    --epochs 200 \\
    --learning-rate 2e-4 \\
    --gradient-accumulation 32 \\
    --mixed-precision \\
    --gradient-checkpointing \\
    --cpu-offload \\
    --num-workers 1 \\
    --output-dir experiments/ultra_memory_optimized \\
    --no-wandb
"""
    
    print("💡 Suggested memory-optimized commands:")
    print(command)
    
    # Save to file
    with open("memory_optimized_commands.txt", "w") as f:
        f.write(command)
    
    print("📝 Commands saved to memory_optimized_commands.txt")


def diagnose_memory_issue():
    """Diagnose the specific memory issue."""
    
    print("🔍 Diagnosing memory issue...")
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return
    
    # Get GPU info
    device_name = torch.cuda.get_device_name()
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
    
    print(f"🖥️  GPU: {device_name}")
    print(f"💾 Total memory: {total_memory:.1f} GB")
    
    # Analyze the error from the original command
    original_config = {
        'batch_size': 64,
        'sequence_length': 1024,
        'hidden_dim': 512,  # Estimated from hybrid model
        'num_layers': 12,   # Estimated
        'num_heads': 16,    # From config
        'gradient_accumulation': 8
    }
    
    # Estimate memory usage
    def estimate_memory(config):
        # Rough estimation for transformer model
        params = config['hidden_dim'] ** 2 * config['num_layers'] * 4  # Attention layers
        params += config['hidden_dim'] * 4 * config['hidden_dim'] * config['num_layers']  # FFN
        
        # Activations (attention is the main culprit)
        attention_mem = config['batch_size'] * config['num_heads'] * config['sequence_length'] ** 2
        hidden_mem = config['batch_size'] * config['sequence_length'] * config['hidden_dim'] * config['num_layers']
        
        # Convert to GB (assuming float32)
        total_gb = (params + attention_mem + hidden_mem) * 4 / 1e9
        return total_gb
    
    original_memory = estimate_memory(original_config)
    print(f"📊 Estimated memory usage with original config: {original_memory:.1f} GB")
    
    if original_memory > total_memory * 0.8:
        print("❌ Original configuration exceeds available memory!")
        
        # Suggest optimized config
        optimized_config = {
            'batch_size': 4,
            'sequence_length': 512,
            'hidden_dim': 256,
            'num_layers': 6,
            'num_heads': 8,
            'gradient_accumulation': 16
        }
        
        optimized_memory = estimate_memory(optimized_config)
        print(f"✅ Optimized configuration memory usage: {optimized_memory:.1f} GB")
        
        print("\n🔧 Recommended changes:")
        for key in original_config:
            if key in optimized_config and original_config[key] != optimized_config[key]:
                print(f"   {key}: {original_config[key]} → {optimized_config[key]}")


def main():
    """Main function to apply all fixes."""
    
    print("🚀 CUDA Memory Issue Fix Script")
    print("=" * 50)
    
    # Apply immediate fixes
    apply_immediate_memory_fixes()
    
    print("\n" + "=" * 50)
    
    # Diagnose the issue
    diagnose_memory_issue()
    
    print("\n" + "=" * 50)
    
    # Create optimized commands
    create_memory_optimized_command()
    
    print("\n" + "=" * 50)
    print("🎉 Memory optimization fixes applied!")
    print("\nNext steps:")
    print("1. Use the memory-optimized training script: scripts/train_memory_optimized.py")
    print("2. Or apply the suggested parameter changes to your current command")
    print("3. Monitor memory usage during training")
    print("4. Further reduce parameters if still getting OOM errors")


if __name__ == "__main__":
    main()
