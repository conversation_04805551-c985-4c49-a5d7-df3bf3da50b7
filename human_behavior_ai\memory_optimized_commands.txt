
# Memory-optimized training command for 4GB GPU:
python scripts/train_memory_optimized.py \
    --data-path data/sessions \
    --model-type hybrid \
    --batch-size 4 \
    --sequence-length 512 \
    --hidden-dim 256 \
    --num-layers 6 \
    --num-heads 8 \
    --epochs 200 \
    --learning-rate 2e-4 \
    --gradient-accumulation 16 \
    --mixed-precision \
    --gradient-checkpointing \
    --num-workers 2 \
    --output-dir experiments/memory_optimized \
    --no-wandb

# Alternative: Even more aggressive memory optimization
python scripts/train_memory_optimized.py \
    --data-path data/sessions \
    --model-type hybrid \
    --batch-size 2 \
    --sequence-length 256 \
    --hidden-dim 128 \
    --num-layers 4 \
    --num-heads 4 \
    --epochs 200 \
    --learning-rate 2e-4 \
    --gradient-accumulation 32 \
    --mixed-precision \
    --gradient-checkpointing \
    --cpu-offload \
    --num-workers 1 \
    --output-dir experiments/ultra_memory_optimized \
    --no-wandb
