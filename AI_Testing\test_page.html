<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Human Behavior Testing Page</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #667eea;
            min-height: 100vh;
            color: #333;
            will-change: auto;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transform: translateZ(0);
        }

        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .input-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
        }

        .input-section h2 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        #textInput {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            border: 2px solid #cbd5e0;
            border-radius: 8px;
            box-sizing: border-box;
            transition: all 0.3s ease;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        #textInput:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .scrollable-area {
            margin-top: 30px;
            padding: 20px;
            background: #ffffff;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        .scrollable-area h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .content-block {
            margin-bottom: 25px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            will-change: transform;
        }

        .content-block:hover {
            transform: translateX(3px);
        }

        .content-block h3 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .content-block p {
            line-height: 1.6;
            color: #718096;
            margin-bottom: 10px;
        }

        /* Mouse pointer styles - optimized dual cursor */
        #circle {
            position: fixed;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(255, 0, 0, 0.4);
            pointer-events: none;
            transform: translate(-50%, -50%);
            z-index: 9998;
            will-change: transform;
        }

        #dot {
            position: fixed;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: red;
            pointer-events: none;
            transform: translate(-50%, -50%);
            z-index: 9999;
            will-change: transform;
        }

        .click-effect {
            animation: clickPulse 0.2s ease-out;
        }

        @keyframes clickPulse {
            0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
        }

        .mouse-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            z-index: 10001;
            transform: translateZ(0);
        }

        .status-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            z-index: 10001;
            transform: translateZ(0);
        }

        .ai-active {
            background: rgba(34, 197, 94, 0.9) !important;
        }

        /* Scrollbar styling */
        .scrollable-area::-webkit-scrollbar {
            width: 12px;
        }

        .scrollable-area::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 6px;
        }

        .scrollable-area::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 6px;
        }

        .scrollable-area::-webkit-scrollbar-thumb:hover {
            background: #5a67d8;
        }

        /* AI activity indicator - no animations */

        .ai-activity #circle {
            background-color: rgba(34, 197, 94, 0.4);
        }

        .ai-activity #dot {
            background-color: #22c55e;
        }
    </style>
</head>
<body>
    <div id="circle"></div>
    <div id="dot"></div>
    
    <div class="mouse-info">
        <div>Mouse: <span id="mouseX">0</span>, <span id="mouseY">0</span></div>
        <div>Viewport: <span id="viewportW">0</span> x <span id="viewportH">0</span></div>
    </div>

    <div class="status-indicator" id="statusIndicator">
        Status: Ready
    </div>

    <div class="container">
        <h1>🤖 AI Human Behavior Testing Interface</h1>
        
        <div class="input-section">
            <h2>📝 Text Input Field</h2>
            <input type="text" id="textInput" placeholder="AI will type here when commanded..." autocomplete="off">
        </div>

        <div class="scrollable-area" id="scrollableArea">
            <h2>📜 Scrollable Content Area</h2>
            
            <div class="content-block">
                <h3>🎯 AI Testing Overview</h3>
                <p>This page is designed to test AI-driven human behavior simulation. The AI model has been trained on real human interaction data to produce natural, human-like movements, typing patterns, and scrolling behaviors.</p>
                <p>The red pointer shows real-time mouse position with sub-pixel accuracy. All interactions are generated by the AI model to evade bot detection systems.</p>
            </div>

            <div class="content-block">
                <h3>🖱️ Mouse Movement Testing</h3>
                <p>The AI generates natural mouse movements with realistic acceleration, deceleration, and micro-movements that mimic human motor control patterns.</p>
                <p>Movement trajectories include natural curves, hesitations, and corrections that are characteristic of human mouse usage.</p>
            </div>

            <div class="content-block">
                <h3>⌨️ Typing Behavior Simulation</h3>
                <p>AI-generated typing includes realistic timing variations, occasional typos and corrections, natural pauses between words, and human-like rhythm patterns.</p>
                <p>The model accounts for finger positioning, common typing errors, and the natural flow of human text input.</p>
            </div>

            <div class="content-block">
                <h3>📜 Scrolling Pattern Analysis</h3>
                <p>Scrolling behaviors include variable speeds, natural acceleration and deceleration, realistic pause patterns, and human-like reading rhythms.</p>
                <p>The AI model generates scrolling that matches natural reading and browsing patterns observed in real user data.</p>
            </div>

            <div class="content-block">
                <h3>🔬 Advanced Detection Evasion</h3>
                <p>The AI model incorporates advanced techniques to evade sophisticated bot detection systems including behavioral biometrics, timing analysis, and pattern recognition.</p>
                <p>All generated interactions are designed to pass even the most advanced anti-bot systems used by modern web applications.</p>
            </div>

            <div class="content-block">
                <h3>📊 Real-time Monitoring</h3>
                <p>This interface provides real-time feedback on mouse position, viewport dimensions, and AI activity status.</p>
                <p>The visual indicators help monitor the AI's behavior generation and ensure proper functionality.</p>
            </div>

            <div class="content-block">
                <h3>🎮 Interactive Commands</h3>
                <p>Use the terminal interface to command the AI:</p>
                <p><strong>move</strong> - Generate natural mouse movement to random location</p>
                <p><strong>type &lt;text&gt;</strong> - Generate human-like typing of specified text</p>
                <p><strong>scroll</strong> - Generate natural scrolling behavior in this area</p>
            </div>

            <div class="content-block">
                <h3>🛡️ Stealth Technology</h3>
                <p>The underlying nodriver framework provides advanced stealth capabilities including WebDriver signature removal, fingerprint masking, and CDP-based control.</p>
                <p>Combined with AI-generated human behavior, this creates an extremely sophisticated automation system.</p>
            </div>

            <div class="content-block">
                <h3>📈 Performance Metrics</h3>
                <p>The AI model processes behavioral patterns in real-time, generating responses that maintain human-like characteristics while adapting to different contexts and requirements.</p>
                <p>All interactions are optimized for both realism and performance to ensure smooth operation.</p>
            </div>

            <div class="content-block">
                <h3>🔧 Technical Implementation</h3>
                <p>Built using a hybrid neural network architecture combining transformer, VAE, and GAN components trained on real human interaction data.</p>
                <p>The model generates multi-modal behavioral sequences including mouse trajectories, typing rhythms, and scrolling patterns.</p>
            </div>
        </div>
    </div>

    <script>
        // Mouse tracking and pointer display
        const circle = document.getElementById('circle');
        const dot = document.getElementById('dot');
        const mouseXSpan = document.getElementById('mouseX');
        const mouseYSpan = document.getElementById('mouseY');
        const viewportWSpan = document.getElementById('viewportW');
        const viewportHSpan = document.getElementById('viewportH');
        const statusIndicator = document.getElementById('statusIndicator');

        // Update viewport dimensions
        function updateViewportInfo() {
            viewportWSpan.textContent = window.innerWidth;
            viewportHSpan.textContent = window.innerHeight;
        }

        // Track mouse movement with extreme precision and no delay
        window.addEventListener('mousemove', (e) => {
            circle.style.left = `${e.clientX}px`;
            circle.style.top = `${e.clientY}px`;
            dot.style.left = `${e.clientX}px`;
            dot.style.top = `${e.clientY}px`;
            mouseXSpan.textContent = e.clientX;
            mouseYSpan.textContent = e.clientY;
        }, { passive: true });

        // Add click effect
        window.addEventListener('click', () => {
            dot.classList.remove('click-effect');
            void dot.offsetWidth; // Trigger reflow to restart animation
            dot.classList.add('click-effect');
        }, { passive: true });

        // Update viewport info on resize
        window.addEventListener('resize', updateViewportInfo);
        updateViewportInfo();

        // AI activity indicator
        window.setAIActive = function(active) {
            if (active) {
                statusIndicator.textContent = 'Status: AI Active';
                statusIndicator.classList.add('ai-active');
                document.body.classList.add('ai-activity');
            } else {
                statusIndicator.textContent = 'Status: Ready';
                statusIndicator.classList.remove('ai-active');
                document.body.classList.remove('ai-activity');
            }
        };

        // Expose functions for external control
        window.getScrollableArea = function() {
            return document.getElementById('scrollableArea');
        };

        window.getTextInput = function() {
            return document.getElementById('textInput');
        };

        // Initialize
        console.log('AI Testing Page Loaded - Ready for commands');
    </script>
</body>
</html>
