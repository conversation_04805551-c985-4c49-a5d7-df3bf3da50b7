"""
Dummy data loaders for testing and development.
"""

import torch
from torch.utils.data import Dataset, DataLoader
from typing import Di<PERSON>, <PERSON><PERSON>, Optional
import numpy as np


class DummyBehaviorDataset(Dataset):
    """Dummy dataset for testing human behavior models."""
    
    def __init__(self, 
                 num_samples: int = 1000,
                 sequence_length: int = 512,
                 mouse_dim: int = 12,
                 keyboard_dim: int = 12,
                 scroll_dim: int = 6,
                 context_dim: int = 16):
        """
        Initialize dummy dataset.
        
        Args:
            num_samples: Number of samples in the dataset
            sequence_length: Length of each sequence
            mouse_dim: Dimension of mouse features
            keyboard_dim: Dimension of keyboard features
            scroll_dim: Dimension of scroll features
            context_dim: Dimension of context features
        """
        self.num_samples = num_samples
        self.sequence_length = sequence_length
        self.mouse_dim = mouse_dim
        self.keyboard_dim = keyboard_dim
        self.scroll_dim = scroll_dim
        self.context_dim = context_dim
        
        # Pre-generate some data to make it more realistic
        self._generate_base_patterns()
    
    def _generate_base_patterns(self):
        """Generate base patterns for more realistic dummy data."""
        # Mouse movement patterns (smooth trajectories)
        self.mouse_patterns = []
        for _ in range(10):  # 10 different patterns
            t = np.linspace(0, 4*np.pi, self.sequence_length)
            x = np.sin(t) * 100 + np.random.normal(0, 5, self.sequence_length)
            y = np.cos(t) * 100 + np.random.normal(0, 5, self.sequence_length)
            vx = np.gradient(x)
            vy = np.gradient(y)
            ax = np.gradient(vx)
            ay = np.gradient(vy)
            
            # Add button states and other features
            buttons = np.random.choice([0, 1], size=(self.sequence_length, 3))  # Left, right, middle
            wheel = np.random.normal(0, 0.1, self.sequence_length)
            
            pattern = np.column_stack([x, y, vx, vy, ax, ay, buttons, wheel.reshape(-1, 1)])
            # Pad or truncate to match mouse_dim
            if pattern.shape[1] < self.mouse_dim:
                padding = np.zeros((self.sequence_length, self.mouse_dim - pattern.shape[1]))
                pattern = np.column_stack([pattern, padding])
            else:
                pattern = pattern[:, :self.mouse_dim]
            
            self.mouse_patterns.append(pattern)
        
        # Keyboard patterns (typing bursts)
        self.keyboard_patterns = []
        for _ in range(10):
            pattern = np.zeros((self.sequence_length, self.keyboard_dim))
            
            # Simulate typing bursts
            for _ in range(np.random.randint(1, 5)):
                start = np.random.randint(0, self.sequence_length - 50)
                length = np.random.randint(10, 50)
                end = min(start + length, self.sequence_length)
                
                # Key codes (simplified)
                pattern[start:end, 0] = np.random.randint(65, 90, end - start)  # A-Z
                # Timing features
                pattern[start:end, 1] = np.random.exponential(0.1, end - start)  # Inter-key intervals
                # Modifiers
                pattern[start:end, 2:5] = np.random.choice([0, 1], size=(end - start, 3))  # Ctrl, Shift, Alt
            
            self.keyboard_patterns.append(pattern)
    
    def __len__(self) -> int:
        return self.num_samples
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a single sample."""
        # Select random patterns
        mouse_pattern = self.mouse_patterns[idx % len(self.mouse_patterns)]
        keyboard_pattern = self.keyboard_patterns[idx % len(self.keyboard_patterns)]
        
        # Add some noise for variation
        mouse_data = mouse_pattern + np.random.normal(0, 0.1, mouse_pattern.shape)
        keyboard_data = keyboard_pattern + np.random.normal(0, 0.01, keyboard_pattern.shape)
        
        # Generate scroll data (less frequent)
        scroll_data = np.zeros((self.sequence_length, self.scroll_dim))
        scroll_events = np.random.poisson(0.1, self.sequence_length)  # Low frequency
        scroll_data[:, 0] = scroll_events * np.random.normal(0, 1, self.sequence_length)  # deltaY
        scroll_data[:, 1] = scroll_events * np.random.normal(0, 0.5, self.sequence_length)  # deltaX
        
        # Generate context data (relatively stable)
        context_data = np.random.normal(0, 1, (self.sequence_length, self.context_dim))
        # Make context more stable (changes slowly)
        for i in range(1, self.sequence_length):
            context_data[i] = 0.9 * context_data[i-1] + 0.1 * context_data[i]
        
        # Combine all modalities
        sequences = np.concatenate([
            mouse_data,
            keyboard_data,
            scroll_data,
            context_data
        ], axis=1)
        
        # Create targets (next step prediction)
        targets = np.roll(sequences, -1, axis=0)
        targets[-1] = sequences[-1]  # Last target is same as last input
        
        # Generate rewards (for RL training)
        rewards = np.random.beta(2, 5, self.sequence_length)  # Skewed towards lower values
        
        return {
            'sequences': torch.FloatTensor(sequences),
            'targets': torch.FloatTensor(targets),
            'rewards': torch.FloatTensor(rewards),
            'mouse': torch.FloatTensor(mouse_data),
            'keyboard': torch.FloatTensor(keyboard_data),
            'scroll': torch.FloatTensor(scroll_data),
            'context': torch.FloatTensor(context_data),
        }


def create_dummy_data_loaders(batch_size: int = 4,
                            sequence_length: int = 512,
                            num_batches: int = 100,
                            num_workers: int = 0,
                            pin_memory: bool = False) -> Tuple[DataLoader, DataLoader]:
    """
    Create dummy data loaders for testing.
    
    Args:
        batch_size: Batch size
        sequence_length: Sequence length
        num_batches: Number of batches per epoch
        num_workers: Number of data loader workers
        pin_memory: Whether to pin memory
    
    Returns:
        Tuple of (train_loader, val_loader)
    """
    # Calculate number of samples
    train_samples = num_batches * batch_size
    val_samples = max(10, num_batches // 10) * batch_size
    
    # Create datasets
    train_dataset = DummyBehaviorDataset(
        num_samples=train_samples,
        sequence_length=sequence_length
    )
    
    val_dataset = DummyBehaviorDataset(
        num_samples=val_samples,
        sequence_length=sequence_length
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True
    )
    
    return train_loader, val_loader


def test_dummy_data_loader():
    """Test the dummy data loader."""
    train_loader, val_loader = create_dummy_data_loaders(
        batch_size=2,
        sequence_length=128,
        num_batches=5
    )
    
    print("Testing dummy data loader...")
    
    # Test train loader
    for i, batch in enumerate(train_loader):
        print(f"Train batch {i}:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape} ({value.dtype})")
        
        if i >= 2:  # Only test first few batches
            break
    
    # Test val loader
    for i, batch in enumerate(val_loader):
        print(f"Val batch {i}:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape} ({value.dtype})")
        
        if i >= 1:  # Only test first batch
            break
    
    print("Dummy data loader test completed successfully!")


if __name__ == "__main__":
    test_dummy_data_loader()
