#!/usr/bin/env python3
"""
Quick test script to verify the AI automation fixes work correctly.
"""

import asyncio
from ai_automation import AIAutomation


async def test_automation():
    """Test the fixed automation system."""
    print("🧪 Testing AI Automation Fixes")
    print("=" * 40)
    
    automation = AIAutomation()
    
    try:
        # Test browser startup
        print("1. Testing browser startup...")
        await automation.start_browser()
        print("✅ Browser started successfully")
        
        # Test page loading
        print("2. Testing page loading...")
        await automation.load_test_page()
        print("✅ Page loaded successfully")
        
        # Test mouse movement
        print("3. Testing AI mouse movement...")
        await automation.ai_move_mouse()
        print("✅ Mouse movement completed")
        
        # Test typing
        print("4. Testing AI typing...")
        await automation.ai_type_text("Hello, AI testing!")
        print("✅ Typing completed")
        
        # Test scrolling (the previously problematic function)
        print("5. Testing AI scrolling...")
        await automation.ai_scroll()
        print("✅ Scrolling completed")
        
        print("\n🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Test browser closing
        print("6. Testing browser close...")
        await automation.close()
        print("✅ Browser closed successfully")


if __name__ == "__main__":
    asyncio.run(test_automation())
