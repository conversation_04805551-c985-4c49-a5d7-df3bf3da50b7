{"model_config": {"hidden_dim": 256, "num_layers": 6, "num_heads": 8, "dropout": 0.1, "activation": "gelu", "input_dim": 46, "output_dim": 46, "sequence_length": 512, "mouse_dim": 12, "keyboard_dim": 12, "scroll_dim": 6, "context_dim": 16, "learning_rate": 0.0002, "weight_decay": 1e-05, "gradient_clip": 1.0, "use_positional_encoding": true, "use_layer_norm": true, "use_residual_connections": true, "use_attention_dropout": true, "model_specific": {"use_flash_attention": true, "use_rotary_embeddings": false, "gradient_checkpointing": true, "latent_dim": 128, "beta": 0.1, "use_hierarchical_prior": false, "use_spectral_norm": false, "gradient_penalty_weight": 5.0, "transformer_weight": 0.5, "vae_weight": 0.25, "gan_weight": 0.25, "fusion_strategy": "concat"}}, "training_config": {"batch_size": 4, "val_batch_size": 4, "num_epochs": 200, "learning_rate": 0.0002, "weight_decay": 1e-05, "warmup_steps": 500, "gradient_clip": 1.0, "mixed_precision": true, "gradient_accumulation_steps": 16, "checkpoint_frequency": 2000, "validation_frequency": 1000, "log_frequency": 50, "num_workers": 2, "pin_memory": false, "early_stopping_patience": 15}, "memory_optimizations": {"cuda_memory_fraction": 0.85, "enable_flash_sdp": true, "enable_mem_efficient_sdp": true, "enable_math_sdp": false, "gradient_checkpointing": true, "cpu_offload": false, "attention_chunk_size": 512}, "hardware_settings": {"target_gpu_memory_gb": 4.0, "min_free_memory_gb": 0.5, "memory_monitoring": true}}