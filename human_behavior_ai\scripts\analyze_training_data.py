#!/usr/bin/env python3
"""
Training Data Analysis Tool
Analyzes the current training data to identify and fix the stationary mouse problem.
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import List, Dict, Tuple
import argparse


class TrainingDataAnalyzer:
    """Analyzes training data to identify problematic patterns."""
    
    def __init__(self, data_dir: str):
        """Initialize the analyzer."""
        self.data_dir = Path(data_dir)
        self.sessions = []
        self.analysis_results = {}
    
    def load_sessions(self):
        """Load all session data files."""
        print("🔍 Loading session data...")
        
        session_files = list(self.data_dir.glob("*_data.json"))
        print(f"Found {len(session_files)} session data files")
        
        for session_file in session_files:
            try:
                with open(session_file, 'r') as f:
                    events = json.load(f)
                
                # Load metadata
                metadata_file = session_file.with_name(session_file.stem.replace('_data', '') + '.json')
                metadata = {}
                if metadata_file.exists():
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                
                self.sessions.append({
                    'file': session_file,
                    'events': events,
                    'metadata': metadata
                })
                print(f"✅ Loaded {session_file.name}: {len(events)} events")
                
            except Exception as e:
                print(f"❌ Failed to load {session_file}: {e}")
    
    def analyze_mouse_movement(self, session: Dict) -> Dict:
        """Analyze mouse movement patterns in a session."""
        events = session['events']
        
        # Extract mouse positions and velocities
        positions = []
        velocities = []
        timestamps = []
        task_ids = []
        
        for event in events:
            if event.get('type') == 'mouse_position':
                data = event.get('data', {})
                pos = data.get('position', {})
                vel = data.get('velocity', {})
                
                positions.append([pos.get('x', 0), pos.get('y', 0)])
                velocities.append([vel.get('x', 0), vel.get('y', 0), vel.get('magnitude', 0)])
                timestamps.append(event.get('timestamp', 0))
                task_ids.append(data.get('taskId', 0))
        
        positions = np.array(positions)
        velocities = np.array(velocities)
        timestamps = np.array(timestamps)
        task_ids = np.array(task_ids)
        
        # Analyze movement patterns
        analysis = {
            'total_events': int(len(positions)),
            'unique_positions': int(len(np.unique(positions, axis=0))),
            'stationary_events': int(np.sum(velocities[:, 2] == 0)),  # magnitude == 0
            'movement_events': int(np.sum(velocities[:, 2] > 0)),
            'unique_task_ids': np.unique(task_ids).tolist(),
            'position_variance': np.var(positions, axis=0).tolist(),
            'velocity_stats': {
                'mean_magnitude': float(np.mean(velocities[:, 2])),
                'max_magnitude': float(np.max(velocities[:, 2])),
                'std_magnitude': float(np.std(velocities[:, 2]))
            }
        }
        
        # Calculate stationary periods
        stationary_periods = []
        current_period_start = None
        
        for i, vel_mag in enumerate(velocities[:, 2]):
            if vel_mag == 0:
                if current_period_start is None:
                    current_period_start = i
            else:
                if current_period_start is not None:
                    period_length = i - current_period_start
                    if period_length > 10:  # More than 10 consecutive stationary events
                        stationary_periods.append({
                            'start': current_period_start,
                            'end': i,
                            'length': period_length,
                            'duration_ms': timestamps[i] - timestamps[current_period_start] if i < len(timestamps) else 0
                        })
                    current_period_start = None
        
        analysis['stationary_periods'] = stationary_periods
        analysis['long_stationary_periods'] = len([p for p in stationary_periods if p['length'] > 100])
        
        return analysis
    
    def analyze_all_sessions(self):
        """Analyze all loaded sessions."""
        print("\n📊 Analyzing sessions...")
        
        for i, session in enumerate(self.sessions):
            print(f"\nAnalyzing session {i+1}/{len(self.sessions)}: {session['file'].name}")
            
            mouse_analysis = self.analyze_mouse_movement(session)
            
            print(f"  📍 Total events: {mouse_analysis['total_events']:,}")
            print(f"  📍 Unique positions: {mouse_analysis['unique_positions']:,}")
            print(f"  🛑 Stationary events: {mouse_analysis['stationary_events']:,} ({mouse_analysis['stationary_events']/mouse_analysis['total_events']*100:.1f}%)")
            print(f"  🏃 Movement events: {mouse_analysis['movement_events']:,} ({mouse_analysis['movement_events']/mouse_analysis['total_events']*100:.1f}%)")
            print(f"  📋 Task IDs: {list(mouse_analysis['unique_task_ids'])}")
            print(f"  ⏱️ Long stationary periods: {mouse_analysis['long_stationary_periods']}")
            
            if mouse_analysis['stationary_periods']:
                avg_period_length = np.mean([p['length'] for p in mouse_analysis['stationary_periods']])
                max_period_length = max([p['length'] for p in mouse_analysis['stationary_periods']])
                print(f"  📏 Avg stationary period: {avg_period_length:.1f} events")
                print(f"  📏 Max stationary period: {max_period_length} events")
            
            self.analysis_results[session['file'].name] = mouse_analysis
    
    def generate_report(self, output_file: str = None):
        """Generate a comprehensive analysis report."""
        print("\n📋 Generating Analysis Report...")
        
        report = {
            'summary': {
                'total_sessions': len(self.sessions),
                'total_events': sum(r['total_events'] for r in self.analysis_results.values()),
                'total_stationary_events': sum(r['stationary_events'] for r in self.analysis_results.values()),
                'total_movement_events': sum(r['movement_events'] for r in self.analysis_results.values()),
            },
            'sessions': self.analysis_results
        }
        
        # Calculate overall statistics
        total_events = report['summary']['total_events']
        stationary_events = report['summary']['total_stationary_events']
        movement_events = report['summary']['total_movement_events']
        
        print(f"\n🎯 ANALYSIS SUMMARY:")
        print(f"  📊 Total Sessions: {report['summary']['total_sessions']}")
        print(f"  📊 Total Events: {total_events:,}")
        print(f"  🛑 Stationary Events: {stationary_events:,} ({stationary_events/total_events*100:.1f}%)")
        print(f"  🏃 Movement Events: {movement_events:,} ({movement_events/total_events*100:.1f}%)")
        
        # Identify the main problem
        if stationary_events > movement_events:
            print(f"\n❌ PROBLEM IDENTIFIED:")
            print(f"  🚨 {stationary_events/total_events*100:.1f}% of training data is stationary!")
            print(f"  🚨 This explains the 'circling' behavior in the AI model")
            print(f"  🚨 The model learned that staying still is 'normal' behavior")
        
        # Save report
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\n💾 Report saved to: {output_file}")
        
        return report
    
    def visualize_movement_patterns(self, session_index: int = 0, save_plots: bool = True):
        """Create visualizations of movement patterns."""
        if session_index >= len(self.sessions):
            print(f"❌ Session index {session_index} out of range")
            return
        
        session = self.sessions[session_index]
        events = session['events']
        
        # Extract data for plotting
        positions = []
        velocities = []
        timestamps = []
        
        for event in events:
            if event.get('type') == 'mouse_position':
                data = event.get('data', {})
                pos = data.get('position', {})
                vel = data.get('velocity', {})
                
                positions.append([pos.get('x', 0), pos.get('y', 0)])
                velocities.append(vel.get('magnitude', 0))
                timestamps.append(event.get('timestamp', 0))
        
        positions = np.array(positions)
        velocities = np.array(velocities)
        timestamps = np.array(timestamps)
        
        # Create plots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # Plot 1: Mouse trajectory
        ax1.scatter(positions[:, 0], positions[:, 1], c=velocities, cmap='viridis', alpha=0.6, s=1)
        ax1.set_title('Mouse Trajectory (colored by velocity)')
        ax1.set_xlabel('X Position')
        ax1.set_ylabel('Y Position')
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Velocity over time
        time_normalized = (timestamps - timestamps[0]) / 1000  # Convert to seconds
        ax2.plot(time_normalized, velocities, alpha=0.7)
        ax2.set_title('Mouse Velocity Over Time')
        ax2.set_xlabel('Time (seconds)')
        ax2.set_ylabel('Velocity Magnitude')
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Position variance
        ax3.hist(positions[:, 0], bins=50, alpha=0.7, label='X positions')
        ax3.hist(positions[:, 1], bins=50, alpha=0.7, label='Y positions')
        ax3.set_title('Position Distribution')
        ax3.set_xlabel('Position')
        ax3.set_ylabel('Frequency')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Plot 4: Velocity distribution
        ax4.hist(velocities, bins=50, alpha=0.7)
        ax4.set_title('Velocity Distribution')
        ax4.set_xlabel('Velocity Magnitude')
        ax4.set_ylabel('Frequency')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plots:
            plot_file = self.data_dir / f"movement_analysis_{session['file'].stem}.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            print(f"📊 Plots saved to: {plot_file}")
        
        plt.show()


def main():
    """Main analysis function."""
    parser = argparse.ArgumentParser(description='Analyze training data for mouse movement patterns')
    parser.add_argument('--data-dir', default='data/sessions', help='Directory containing session data')
    parser.add_argument('--output', default='training_data_analysis.json', help='Output file for analysis report')
    parser.add_argument('--visualize', action='store_true', help='Generate visualization plots')
    
    args = parser.parse_args()
    
    print("🔬 Training Data Analysis Tool")
    print("=" * 50)
    
    # Initialize analyzer
    analyzer = TrainingDataAnalyzer(args.data_dir)
    
    # Load and analyze data
    analyzer.load_sessions()
    
    if not analyzer.sessions:
        print("❌ No session data found!")
        return
    
    analyzer.analyze_all_sessions()
    
    # Generate report
    report = analyzer.generate_report(args.output)
    
    # Create visualizations if requested
    if args.visualize and analyzer.sessions:
        print("\n📊 Creating visualizations...")
        analyzer.visualize_movement_patterns(save_plots=True)
    
    print("\n✅ Analysis complete!")


if __name__ == "__main__":
    main()
