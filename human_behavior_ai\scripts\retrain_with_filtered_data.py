#!/usr/bin/env python3
"""
Retrain Model with Filtered Data
Retrains the AI model using filtered data to fix the stationary behavior problem.
"""

import sys
import os
from pathlib import Path
import argparse
import torch
import subprocess

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_data_analysis(data_dir: str):
    """Run data analysis to understand the current data."""
    print("🔍 Step 1: Analyzing current training data...")
    
    analysis_script = project_root / "scripts" / "analyze_training_data.py"
    cmd = [
        sys.executable, str(analysis_script),
        "--data-dir", data_dir,
        "--output", "original_data_analysis.json",
        "--visualize"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Data analysis completed")
            print(result.stdout)
        else:
            print("❌ Data analysis failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running data analysis: {e}")
        return False
    
    return True


def generate_synthetic_data(output_dir: str, num_sessions: int = 10):
    """Generate synthetic training data."""
    print(f"🤖 Step 2: Generating {num_sessions} synthetic training sessions...")
    
    synthetic_script = project_root / "scripts" / "generate_synthetic_data.py"
    cmd = [
        sys.executable, str(synthetic_script),
        "--output-dir", output_dir,
        "--num-sessions", str(num_sessions),
        "--duration", "3"  # 3 minutes per session
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Synthetic data generation completed")
            print(result.stdout)
        else:
            print("❌ Synthetic data generation failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error generating synthetic data: {e}")
        return False
    
    return True


def filter_existing_data(input_dir: str, output_dir: str):
    """Filter existing training data to remove stationary periods."""
    print("🔧 Step 3: Filtering existing training data...")
    
    filter_script = project_root / "scripts" / "filter_training_data.py"
    cmd = [
        sys.executable, str(filter_script),
        "--input-dir", input_dir,
        "--output-dir", output_dir,
        "--min-movement", "3",
        "--max-stationary", "5"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Data filtering completed")
            print(result.stdout)
        else:
            print("❌ Data filtering failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error filtering data: {e}")
        return False
    
    return True


def combine_datasets(filtered_dir: str, synthetic_dir: str, combined_dir: str):
    """Combine filtered and synthetic datasets."""
    print("🔗 Step 4: Combining filtered and synthetic datasets...")
    
    filtered_path = Path(filtered_dir)
    synthetic_path = Path(synthetic_dir)
    combined_path = Path(combined_dir)
    
    combined_path.mkdir(exist_ok=True)
    
    # Copy filtered data
    filtered_files = list(filtered_path.glob("*.json"))
    for file in filtered_files:
        dest = combined_path / f"filtered_{file.name}"
        import shutil
        shutil.copy2(file, dest)
    
    # Copy synthetic data
    synthetic_files = list(synthetic_path.glob("*.json"))
    for file in synthetic_files:
        dest = combined_path / file.name
        import shutil
        shutil.copy2(file, dest)
    
    total_files = len(filtered_files) + len(synthetic_files)
    print(f"✅ Combined dataset created: {total_files} files")
    print(f"   📁 Filtered files: {len(filtered_files)}")
    print(f"   🤖 Synthetic files: {len(synthetic_files)}")
    print(f"   📂 Output directory: {combined_path}")
    
    return True


def retrain_model(data_dir: str, output_dir: str, epochs: int = 50):
    """Retrain the model with the combined dataset."""
    print(f"🧠 Step 5: Retraining model for {epochs} epochs...")
    
    # Use the existing training script
    train_script = project_root / "scripts" / "train_real_data_optimized.py"
    
    cmd = [
        sys.executable, str(train_script),
        "--data-path", data_dir,
        "--epochs", str(epochs),
        "--batch-size", "4",
        "--gradient-accumulation", "32",
        "--output-dir", output_dir,
        "--no-wandb"
    ]
    
    try:
        print(f"🚀 Starting training with command:")
        print(f"   {' '.join(cmd)}")
        
        result = subprocess.run(cmd, cwd=project_root)
        if result.returncode == 0:
            print("✅ Model retraining completed successfully!")
            return True
        else:
            print("❌ Model retraining failed")
            return False
    except Exception as e:
        print(f"❌ Error during retraining: {e}")
        return False


def test_retrained_model(model_dir: str):
    """Test the retrained model to verify the fix."""
    print("🧪 Step 6: Testing retrained model...")
    
    try:
        # Import the AI behavior generator
        sys.path.insert(0, str(project_root / "AI_Testing"))
        from ai_behavior_generator import AIBehaviorGenerator
        
        # Initialize with the retrained model
        generator = AIBehaviorGenerator(model_dir)
        
        if generator.model_loaded:
            print("✅ Retrained model loaded successfully!")
            
            # Test mouse movement generation
            print("🖱️ Testing mouse movement generation...")
            trajectory = generator.generate_mouse_trajectory(100, 100, 500, 400, 2.0)
            
            # Analyze the trajectory for stationary periods
            stationary_count = 0
            movement_count = 0
            
            for i in range(1, len(trajectory)):
                prev_x, prev_y, _ = trajectory[i-1]
                curr_x, curr_y, _ = trajectory[i]
                
                distance = ((curr_x - prev_x)**2 + (curr_y - prev_y)**2)**0.5
                
                if distance < 2:
                    stationary_count += 1
                else:
                    movement_count += 1
            
            total_points = len(trajectory)
            movement_ratio = movement_count / total_points if total_points > 0 else 0
            
            print(f"📊 Trajectory Analysis:")
            print(f"   📍 Total points: {total_points}")
            print(f"   🏃 Movement points: {movement_count} ({movement_ratio*100:.1f}%)")
            print(f"   🛑 Stationary points: {stationary_count} ({(1-movement_ratio)*100:.1f}%)")
            
            if movement_ratio > 0.7:  # At least 70% movement
                print("✅ SUCCESS: Model shows good movement patterns!")
                print("🎉 The stationary behavior issue appears to be fixed!")
                return True
            else:
                print("⚠️ WARNING: Model still shows excessive stationary behavior")
                print("💡 Consider retraining with more synthetic data or different parameters")
                return False
        else:
            print("❌ Failed to load retrained model")
            return False
            
    except Exception as e:
        print(f"❌ Error testing retrained model: {e}")
        return False


def main():
    """Main retraining pipeline."""
    parser = argparse.ArgumentParser(description='Retrain model with filtered data to fix stationary behavior')
    parser.add_argument('--original-data', default='data/sessions', help='Original training data directory')
    parser.add_argument('--epochs', type=int, default=50, help='Number of training epochs')
    parser.add_argument('--synthetic-sessions', type=int, default=15, help='Number of synthetic sessions to generate')
    parser.add_argument('--output-dir', default='experiments/retrained_fixed_behavior', help='Output directory for retrained model')
    
    args = parser.parse_args()
    
    print("🔄 AI Model Retraining Pipeline")
    print("=" * 60)
    print("🎯 Goal: Fix stationary behavior in mouse movement generation")
    print("=" * 60)
    
    # Create working directories
    work_dir = project_root / "data" / "retraining_work"
    work_dir.mkdir(exist_ok=True)
    
    filtered_dir = work_dir / "filtered"
    synthetic_dir = work_dir / "synthetic"
    combined_dir = work_dir / "combined"
    
    # Step 1: Analyze original data
    if not run_data_analysis(args.original_data):
        print("❌ Pipeline failed at data analysis step")
        return 1
    
    # Step 2: Generate synthetic data
    if not generate_synthetic_data(str(synthetic_dir), args.synthetic_sessions):
        print("❌ Pipeline failed at synthetic data generation step")
        return 1
    
    # Step 3: Filter existing data
    if not filter_existing_data(args.original_data, str(filtered_dir)):
        print("❌ Pipeline failed at data filtering step")
        return 1
    
    # Step 4: Combine datasets
    if not combine_datasets(str(filtered_dir), str(synthetic_dir), str(combined_dir)):
        print("❌ Pipeline failed at dataset combination step")
        return 1
    
    # Step 5: Retrain model
    if not retrain_model(str(combined_dir), args.output_dir, args.epochs):
        print("❌ Pipeline failed at model retraining step")
        return 1
    
    # Step 6: Test retrained model
    model_path = Path(args.output_dir) / "final_model"
    if not test_retrained_model(str(model_path)):
        print("⚠️ Model testing indicates potential issues")
        print("💡 The model was retrained, but may need further improvements")
    
    print("\n🎉 RETRAINING PIPELINE COMPLETED!")
    print("=" * 60)
    print(f"📁 Retrained model saved to: {args.output_dir}")
    print(f"🧪 Test the model in AI_Testing with the new model path")
    print("💡 The stationary behavior should now be significantly reduced!")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
