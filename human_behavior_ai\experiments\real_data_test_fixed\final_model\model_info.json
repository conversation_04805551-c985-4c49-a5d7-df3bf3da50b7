{"model_name": "HybridBehaviorModel", "num_parameters": 27670258, "config": {"hidden_dim": 256, "num_layers": 8, "num_heads": 8, "dropout": 0.1, "activation": "gelu", "input_dim": 15, "output_dim": 15, "sequence_length": 1024, "mouse_dim": 6, "keyboard_dim": 1, "scroll_dim": 4, "context_dim": 4, "learning_rate": 0.0002, "weight_decay": 1e-05, "gradient_clip": 1.0, "use_positional_encoding": true, "use_layer_norm": true, "use_residual_connections": true, "use_attention_dropout": true, "model_specific": {"use_flash_attention": true, "use_rotary_embeddings": false, "gradient_checkpointing": true, "latent_dim": 128, "beta": 0.1, "use_hierarchical_prior": false, "use_spectral_norm": false, "gradient_penalty_weight": 5.0, "transformer_weight": 0.5, "vae_weight": 0.25, "gan_weight": 0.25, "fusion_strategy": "mlp"}}}