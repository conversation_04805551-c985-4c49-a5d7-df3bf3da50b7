#!/usr/bin/env python3
"""
Memory-optimized training script for human behavior AI models.
Specifically designed for low-memory GPUs (4GB and below).
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import torch
import torch.nn as nn
from torch.utils.data import DataLoader

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from human_behavior_ai.human_behavior_ai.models.main import create_model
from human_behavior_ai.human_behavior_ai.training.base import TrainingConfig
from human_behavior_ai.human_behavior_ai.training.hybrid_trainer import HybridBehaviorTrainer
from human_behavior_ai.data.loader import create_data_loaders
from human_behavior_ai.human_behavior_ai.utils.logging import setup_logging
from human_behavior_ai.human_behavior_ai.utils.random import set_random_seed
from human_behavior_ai.human_behavior_ai.utils.device import get_device_info, get_memory_usage


def parse_args():
    """Parse command line arguments with memory-optimized defaults."""
    parser = argparse.ArgumentParser(description='Memory-optimized training for human behavior AI')
    
    # Model configuration
    parser.add_argument('--model-type', type=str, default='hybrid',
                       choices=['hybrid', 'transformer', 'vae', 'gan'],
                       help='Type of model to train')
    parser.add_argument('--data-path', type=Path, required=True,
                       help='Path to training data')
    
    # Memory-optimized training parameters
    parser.add_argument('--batch-size', type=int, default=4,
                       help='Batch size (reduced for memory efficiency)')
    parser.add_argument('--sequence-length', type=int, default=512,
                       help='Sequence length (reduced from 1024)')
    parser.add_argument('--gradient-accumulation', type=int, default=16,
                       help='Gradient accumulation steps (increased to maintain effective batch size)')
    
    # Model size parameters
    parser.add_argument('--hidden-dim', type=int, default=256,
                       help='Hidden dimension (reduced from 512)')
    parser.add_argument('--num-layers', type=int, default=6,
                       help='Number of layers (reduced from 12)')
    parser.add_argument('--num-heads', type=int, default=8,
                       help='Number of attention heads')
    
    # Training parameters
    parser.add_argument('--epochs', type=int, default=200,
                       help='Number of training epochs')
    parser.add_argument('--learning-rate', type=float, default=2e-4,
                       help='Learning rate')
    parser.add_argument('--weight-decay', type=float, default=1e-5,
                       help='Weight decay')
    
    # Memory optimization flags
    parser.add_argument('--mixed-precision', action='store_true', default=True,
                       help='Use mixed precision training (enabled by default)')
    parser.add_argument('--gradient-checkpointing', action='store_true',
                       help='Use gradient checkpointing to save memory')
    parser.add_argument('--cpu-offload', action='store_true',
                       help='Offload optimizer states to CPU')
    
    # Hardware settings
    parser.add_argument('--num-workers', type=int, default=2,
                       help='Number of data loader workers (reduced)')
    parser.add_argument('--pin-memory', action='store_false',
                       help='Disable pin memory to save GPU memory')
    
    # Output settings
    parser.add_argument('--output-dir', type=Path, default='experiments/memory_optimized',
                       help='Output directory')
    parser.add_argument('--save-every', type=int, default=2000,
                       help='Save checkpoint every N steps')
    parser.add_argument('--eval-every', type=int, default=1000,
                       help='Evaluate every N steps')
    parser.add_argument('--log-every', type=int, default=50,
                       help='Log every N steps')
    
    # Debugging
    parser.add_argument('--no-wandb', action='store_true',
                       help='Disable Weights & Biases logging')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    return parser.parse_args()


def setup_memory_optimizations():
    """Setup various memory optimizations for CUDA."""
    if torch.cuda.is_available():
        # Set memory fraction to leave some headroom
        torch.cuda.set_per_process_memory_fraction(0.85)
        
        # Enable memory efficient attention backends
        torch.backends.cuda.enable_flash_sdp(True)
        torch.backends.cuda.enable_mem_efficient_sdp(True)
        torch.backends.cuda.enable_math_sdp(False)  # Disable math SDP to save memory
        
        # Set CUDA memory allocator settings
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
        
        # Clear cache
        torch.cuda.empty_cache()
        
        print(f"CUDA optimizations enabled for {torch.cuda.get_device_name()}")
        print(f"Available CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")


def create_memory_optimized_model(args):
    """Create a memory-optimized model configuration."""
    from human_behavior_ai.human_behavior_ai.models.base import ModelConfig
    
    config = ModelConfig(
        # Reduced model dimensions
        hidden_dim=args.hidden_dim,
        num_layers=args.num_layers,
        num_heads=args.num_heads,
        sequence_length=args.sequence_length,
        
        # Input dimensions (keep reasonable)
        mouse_dim=12,  # Reduced from 16
        keyboard_dim=12,  # Reduced from 16
        scroll_dim=6,   # Reduced from 8
        context_dim=16,  # Reduced from 24
        
        # Training settings
        learning_rate=args.learning_rate,
        batch_size=args.batch_size,
        dropout=0.1,
        
        # Memory optimizations
        use_positional_encoding=True,
        use_layer_norm=True,
        use_residual_connections=True,
        use_attention_dropout=True,
        
        # Model-specific optimizations
        model_specific={
            # Transformer optimizations
            'use_flash_attention': True,
            'use_rotary_embeddings': False,  # Disable to save memory
            'gradient_checkpointing': args.gradient_checkpointing,
            
            # VAE optimizations
            'latent_dim': 128,  # Reduced from 256
            'beta': 0.1,
            'use_hierarchical_prior': False,  # Disable to save memory
            
            # GAN optimizations
            'use_spectral_norm': False,  # Disable to save memory
            'gradient_penalty_weight': 5.0,  # Reduced
            
            # Hybrid optimizations
            'transformer_weight': 0.5,
            'vae_weight': 0.25,
            'gan_weight': 0.25,
            'fusion_strategy': 'concat',  # Simpler than attention
        }
    )
    
    return create_model(args.model_type, config)


def main():
    """Main training function with memory optimizations."""
    args = parse_args()
    
    # Setup logging
    setup_logging(level=logging.DEBUG if args.debug else logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Set random seed
    set_random_seed(args.seed)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Setup memory optimizations
    setup_memory_optimizations()
    
    # Log initial memory usage
    memory_info = get_memory_usage()
    logger.info(f"Initial memory usage: {memory_info}")
    
    # Create model
    logger.info("Creating memory-optimized model...")
    model = create_memory_optimized_model(args)
    logger.info(f"Model created with {model.get_num_parameters():,} parameters")
    
    # Log memory after model creation
    memory_info = get_memory_usage()
    logger.info(f"Memory after model creation: {memory_info}")
    
    # Create data loaders with memory-optimized settings
    logger.info("Creating data loaders...")
    try:
        train_loader, val_loader, _ = create_data_loaders(
            data_dir=str(args.data_path),
            batch_size=args.batch_size,
            num_workers=args.num_workers,
            pin_memory=args.pin_memory,
            seed=args.seed
        )
        logger.info("Data loaders created successfully")
    except Exception as e:
        logger.error(f"Failed to create data loaders: {e}")
        logger.info("Creating dummy data loaders for testing...")
        # Create dummy loaders for testing
        from human_behavior_ai.data.dummy import create_dummy_data_loaders
        train_loader, val_loader = create_dummy_data_loaders(
            batch_size=args.batch_size,
            sequence_length=args.sequence_length,
            num_batches=100
        )
    
    # Create training configuration
    training_config = TrainingConfig(
        num_epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        mixed_precision=args.mixed_precision,
        gradient_accumulation_steps=args.gradient_accumulation,
        checkpoint_frequency=args.save_every,
        validation_frequency=args.eval_every,
        log_frequency=args.log_every,
        output_dir=str(output_dir),
        wandb_project=None if args.no_wandb else "human-behavior-ai-memory-optimized",
        num_workers=args.num_workers,
        pin_memory=args.pin_memory,
    )
    
    # Create trainer
    logger.info("Creating trainer...")
    trainer = HybridBehaviorTrainer(model, training_config)
    
    # Log memory before training
    memory_info = get_memory_usage()
    logger.info(f"Memory before training: {memory_info}")
    
    # Start training
    logger.info("Starting memory-optimized training...")
    try:
        history = trainer.train(train_loader, val_loader)
        logger.info("Training completed successfully!")
        
        # Log final memory usage
        memory_info = get_memory_usage()
        logger.info(f"Final memory usage: {memory_info}")
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error(f"CUDA out of memory: {e}")
        logger.error("Try reducing batch size, sequence length, or model dimensions further")
        return 1
    except Exception as e:
        logger.error(f"Training failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
